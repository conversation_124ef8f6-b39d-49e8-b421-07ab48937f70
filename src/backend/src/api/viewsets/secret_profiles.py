from django.core.exceptions import Field<PERSON>rror
from django_filters.rest_framework import Django<PERSON>ilt<PERSON><PERSON><PERSON>end
from rest_framework.exceptions import ParseError
from rest_framework.mixins import ListModelMixin
from rest_framework.viewsets import GenericViewSet
from src.api.filters import SecretProfileFilter
from src.api.pagination import PageNumberPagination
from src.api.serializers.secret_profiles import SecretProfileListSerializer
from src.core.models import SecretProfile

from .base import SerializerDictMixin


class SecretProfileViewSet(
    ListModelMixin,
    SerializerDictMixin,
    GenericViewSet,
):
    queryset = SecretProfile.objects.all()
    serializers = {
        "list": SecretProfileListSerializer,
    }
    filter_backends = (DjangoFilterBackend,)
    filterset_class = SecretProfileFilter
    pagination_class = PageNumberPagination

    def get_queryset(self):
        qs = super().get_queryset()
        ordering = self.request.query_params.get("ordering")
        if not ordering:
            return qs
        try:
            return qs.order_by(ordering)
        except FieldError:
            raise ParseError(f"Invalid ordering field: {ordering}")
