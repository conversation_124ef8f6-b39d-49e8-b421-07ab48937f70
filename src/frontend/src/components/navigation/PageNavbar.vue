<script setup>
import ShButton from "@/components/generic/ShButton.vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    default: null,
  },
  backRoute: {
    type: Object,
    default: null,
  }
})
</script>

<template>
  <div class="card shadow-lg mb-3">
    <div class="card shadow-lg">
      <div class="card-body p-3">
        <div class="row gx-4 align-items-center">
          <div class="col-auto">
            <div class="d-flex align-items-center">
              <div
                v-if="props.icon"
                class="page-navbar-icon bg-gradient-primary border-radius-lg me-3 d-flex align-items-center justify-content-center"
              >
                <i
                  class="fa text-white"
                  :class="`fa-${props.icon}`"
                />
              </div>
              <h3 class="mb-0 page-navbar-title">
                {{ props.title }}
              </h3>
            </div>
          </div>
          <div class="col ms-auto text-end">
            <sh-button
              v-if="props.backRoute"
              variant="outline"
              color="secondary"
              size="sm"
              @click="$router.push(props.backRoute)"
            >
              <i class="fa fa-chevron-left me-1"></i>
              Back
            </sh-button>
            <span class="ms-2">
              <slot name="actions"/>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-navbar-icon {
  height: 48px;
  width: 48px;
}
.page-navbar-title {
  max-width: 1000px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
@media (max-width: 1540px) {
  .page-navbar-title {
    max-width: 700px;
  }
}
@media (max-width: 1240px) {
  .page-navbar-title {
    max-width: 500px;
  }
}
@media (max-width: 992px) {
  .page-navbar-title {
    max-width: 300px;
  }
}
</style>