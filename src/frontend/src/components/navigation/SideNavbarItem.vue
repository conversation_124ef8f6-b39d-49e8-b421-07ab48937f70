<script setup>
import { useStore } from "vuex";
import { computed } from 'vue';
const store = useStore();

const sideNavbarPinned = computed(() => store.state.sideNavbarPinned);

defineProps({
  // eslint-disable-next-line vue/require-default-prop
  to: {
    type: [Object, String],
    required: false,
  },
  miniIcon: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: null,
  },
  id: {
    type: String,
    required: true,
  }
});
</script>

<template>
  <li class="nav-item">
    <router-link
      v-if="to"
      :id="id"
      class="nav-link"
      :class="{'nav-link-centered': !sideNavbarPinned}"
      :to="to"
    >
      <span
        class="sidenav-mini-icon"
        :class="color"
      >
        <i
          class="nav-icon"
          :class="`${miniIcon}`"
        />
      </span>
      <span
        :class="`sidenav-normal ${color}`"
      >
        <i
          :class="`${miniIcon}`"
          class="me-2"
        ></i>
        {{ text }}
      </span>
    </router-link>
    <div
      v-else
      :id="id"
      class="nav-link cursor-pointer"
      :class="{'nav-link-centered': !sideNavbarPinned}"
    >
      <span
        class="sidenav-mini-icon"
        :class="color"
      >
        <i
          class="nav-icon"
          :class="`${miniIcon}`"
        />
      </span>
      <span
        :class="`sidenav-normal ${color}`"
      >
        <i
          :class="`${miniIcon}`"
          class="me-2"
        ></i>
        {{ text }}
      </span>
    </div>
  </li>
</template>


<style scoped>
.nav-icon {
  width: 25px;
}

.nav-link-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidenav-hovered .nav-link-centered {
  justify-content: start;
}
</style>
