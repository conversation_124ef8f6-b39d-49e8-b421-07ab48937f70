<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import Breadcrumbs from '@/components/navigation/Breadcrumbs.vue';

const store = useStore();
const isNavFixed = computed(() => store.state.isNavFixed);
const darkMode = computed(() => store.state.darkMode);

const toggleSideNavbar = () => store.commit('toggleSideNavbar');
</script>
<template>
  <nav
    id="navbarBlur"
    :class="`${
      !isNavFixed
        ? 'navbar navbar-main navbar-expand-lg px-0 border-radius-xl shadow-none'
        : `navbar navbar-main navbar-expand-lg px-0 border-radius-xl shadow-none position-sticky ${
            darkMode ? 'bg-default' : 'bg-white'
          } left-auto top-2 z-index-sticky`
    }`"
    v-bind="$attrs"
    data-scroll="true"
  >
    <div class="container-fluid">
      <div class="d-flex justify-content-between w-100">
        <div class="d-flex align-items-center justify-content-start">
          <div class="me-2 sidenav-toggler sidenav-toggler-inner d-xl-block d-none">
            <a
              href="#"
              class="p-0 nav-link text-body"
              @click.prevent="toggleSideNavbar"
            >
              <div class="sidenav-toggler-inner">
                <i class="sidenav-toggler-line bg-white"></i>
                <i class="sidenav-toggler-line bg-white"></i>
                <i class="sidenav-toggler-line bg-white"></i>
              </div>
            </a>
          </div>

          <breadcrumbs/>
        </div>

        <div class="d-flex align-items-center">
          <div
            id="navbar"
            class="mt-2 collapse navbar-collapse mt-sm-0 me-md-0 me-sm-4 me-sm-4"
          >
            <ul class="navbar-nav justify-content-end">
              <li class="px-3 nav-item d-flex align-items-center">
                <router-link
                  :to="{ name: 'user-profile' }"
                  class="p-0 nav-link text-white"
                >
                  <i class="cursor-pointer fa fa-user"></i>
                </router-link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>
