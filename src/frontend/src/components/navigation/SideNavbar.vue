<script setup>
import SideNavbarItem from '@/components/navigation/SideNavbarItem.vue';
import {
  computed,
  ref,
  watch,
} from 'vue';
import { useStore } from 'vuex';
import ShAlert from '@/components/generic/ShAlert.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const store = useStore();
const sideNavbarPinned = computed(() => store.state.sideNavbarPinned);
const mouseOver = ref(false);
const showLogoutConfirm = ref(false);

const logout = async () => {
  showLogoutConfirm.value = false;
  await router.push({ name: 'user-logout' });
};

watch(sideNavbarPinned, (newValue) => {
  const baseDiv = document.querySelector("div#app");
  if (newValue) {
    baseDiv.classList.remove("g-sidenav-hidden");
    baseDiv.classList.add("g-sidenav-pinned");
  } else {
    baseDiv.classList.remove("g-sidenav-pinned");
    baseDiv.classList.add("g-sidenav-hidden");
  }
}, { immediate: true });
</script>

<template>
  <div class="min-height-300 position-absolute w-100 bg-gradient-primary"/>

  <aside
    id="sidenav-main"
    class="sidenav ms-3 mt-2 navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl bg-white fixed-start"
    :class="mouseOver ? 'sidenav-hovered' : ''"
    @mouseover="mouseOver = true"
    @mouseout="mouseOver = false"
  >
    <div class="sidenav-header">
      <i
        id="iconSidenav"
        class="top-0 p-3 cursor-pointer fas fa-times text-secondary opacity-5 position-absolute end-0 d-none d-xl-none"
        aria-hidden="true"
      />
      <router-link
        class="m-0 mb-5 navbar-brand w-100 text-center"
        :to="{name: 'dashboard'}"
      >
        <div>
          <img
            src="@/assets/images/logo-sh-color.png"
            class="navbar-brand-img h-100"
            alt="main_logo"
          />
        </div>
        <span class="font-weight-light">
          SekraHub
        </span>
      </router-link>
    </div>

    <hr class="mt-4 horizontal dark"/>
    <div
      id="sidenav-collapse-main"
      class="collapse navbar-collapse overflow-x-hidden w-auto h-auto h-100"
    >
      <ul class="navbar-nav">
        <li class="nav-item">
          <ul class="nav">
            <side-navbar-item
              id="sidenav-dashboard"
              :to="{ name: 'dashboard' }"
              mini-icon="fa fa-bar-chart"
              text="Dashboard"
            />
            <side-navbar-item
              id="sidenav-alert-list"
              :to="{ name: 'alerts' }"
              mini-icon="fa fa-exclamation-triangle"
              text="Alerts"
            />
            <side-navbar-item
              id="sidenav-secret-list"
              :to="{ name: 'secret-list' }"
              mini-icon="fa fa-key"
              text="Secrets"
            />
            <side-navbar-item
              id="sidenav-connector-sync-runs"
              :to="{ name: 'connectors-sync-runs' }"
              mini-icon="fa fa-sync"
              text="Synchronizations"
            />
            <side-navbar-item
              id="sidenav-rule-list"
              :to="{ name: 'rule-list' }"
              mini-icon="fa fa-file"
              text="Rules"
            />
            <side-navbar-item
              id="sidenav-connector-list"
              :to="{ name: 'connector-list' }"
              mini-icon="fa fa-plug"
              text="Connectors"
            />
          </ul>
        </li>
        <li class="nav-item">
          <hr class="mt-0 horizontal dark"/>
          <ul class="nav">
            <side-navbar-item
              id="sidenav-user-profile"
              :to="{ name: 'user-profile' }"
              mini-icon="fa fa-user"
              text="Profile"
            />
          </ul>
          <ul class="nav">
            <side-navbar-item
              id="sidenav-logout"
              color="text-danger"
              mini-icon="fa fa-sign-out"
              text="Logout"
              @click="showLogoutConfirm = true"
            />
          </ul>
        </li>
      </ul>
    </div>
  </aside>
  <sh-alert
    v-if="showLogoutConfirm"
    title="Logout"
    text="Are you sure you want to logout?"
    icon="question"
    footer="You will be logged out of the application."
    cancel-button-color="btn btn-danger"
    confirm-button-text="Logout"
    :show-cancel-button="true"
    cancel-button-text="Cancel"
    @confirmed="logout"
    @dismissed="showLogoutConfirm = false"
  />
</template>
