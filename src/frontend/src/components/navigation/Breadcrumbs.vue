<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const store = useStore();
const breadcrumb = computed(() => store.state.breadcrumb);

const getRouteForDirectory = (directory) => {
  const routeMap = {
    'Dashboard': { name: 'dashboard' },
    'Rules': { name: 'rule-list' },
    'Alerts': { name: 'alerts' },
    'Connectors': { name: 'connector-list' },
    'Secrets': { name: 'secret-list' },
    'Synchronizations': { name: 'connectors-sync-runs' },
    'Profile': { name: 'user-profile' },
  };

  return routeMap[directory] || { name: 'dashboard' };
};

</script>
<template>
  <nav aria-label="breadcrumb">
    <ol class="px-0 pt-0 pb-0 mb-0 bg-transparent breadcrumb me-sm-6">
      <li class="text-sm breadcrumb-item ps-2">
        <router-link to="/">
          <i class="fa fa-home text-white"></i>
        </router-link>
      </li>
      <li
        v-if="breadcrumb.currentDirectory"
        class="text-sm breadcrumb-item text-white"
      >
        <router-link
          :to="getRouteForDirectory(breadcrumb.currentDirectory)"
          class="text-white"
        >
          {{ breadcrumb.currentDirectory }}
        </router-link>
      </li>
      <li
        v-if="breadcrumb.detailPage"
        class="text-sm breadcrumb-item text-white detail-page"
        aria-current="page"
      >
        {{ breadcrumb.detailPage }}
      </li>
    </ol>
  </nav>
</template>
<style scoped>
.detail-page {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400px;
}
</style>
