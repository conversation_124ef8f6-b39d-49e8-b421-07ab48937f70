<script setup>
import { computed, onMounted, ref } from 'vue';
import ShInput from '@/components/generic/ShInput.vue';
import ShSwitch from "@/components/generic/ShSwitch.vue";
import ShBadge from "@/components/generic/ShBadge.vue";

const props = defineProps({
  errors: {
    type: Object,
    default: () => ({}),
  },
  initialData: {
    type: Object,
    default: null,
  },
  testConnectionResult: {
    type: Object,
    default: null,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['submitted', 'cancel', 'test-connection']);

const formData = ref({
  name: null,
  base_url: null,
  username: null,
  account: null,
  api_key: null,
  is_active: true,
  application: 'conjur',
});

const isEditing = ref(false);

const isButtonDisabled = computed(() => {
  return props.isLoading || !formData.value.base_url || !formData.value.username || !formData.value.account || !formData.value.api_key;
});

onMounted(() => {
  if (props.initialData) {
    formData.value = {
      name: props.initialData.name || null,
      base_url: props.initialData.base_url || null,
      username: props.initialData.username || null,
      account: props.initialData.account || null,
      is_active: props.initialData.is_active ?? true,
      application: 'conjur',
    };
  }
});

function editApiToken() {
  isEditing.value = true;
}

function testConnection() {
  emit('test-connection', formData.value);
}
</script>

<template>
  <form @submit.prevent="emit('submitted', formData)">
    <sh-input
      id="connector-form-name"
      v-model="formData.name"
      class="mb-3"
      type="text"
      placeholder="Enter connector name"
      title="Connector Name"
      :errors="props.errors.name"

    />
    <sh-input
      id="connector-form-url"
      v-model="formData.base_url"
      class="mb-3"
      type="url"
      placeholder="E.g. https://conjur.example.com"
      title="Base URL"
      :errors="props.errors.base_url"
    />
    <sh-input
      id="connector-form-username"
      v-model="formData.username"
      class="mb-3"
      type="text"
      placeholder="Enter username"
      title="Username"
      :errors="props.errors.username"
    />
    <sh-input
      id="connector-form-account"
      v-model="formData.account"
      class="mb-3"
      type="text"
      placeholder="Enter account name"
      title="Account"
      :errors="props.errors.account"
    />
    <sh-input
      v-if="!props.initialData"
      id="connector-form-api-key"
      v-model="formData.api_key"
      class="mb-3"
      type="password"
      placeholder="Enter API key or Access token"
      title="API Key / Token"
      :errors="props.errors.api_key"
    />
    <sh-input
      v-if="props.initialData && isEditing"
      id="connector-form-api-key"
      v-model="formData.api_key"
      class="mb-3"
      type="password"
      placeholder="Update API key or Access token"
      title="API Key / Token"
      :errors="props.errors.api_key"
    />
    <div
      v-if="props.initialData && !isEditing"
      class="mb-3"
    >
      <label
        for="connector-form-edit-api-key"
        class="w-100 mb-1">
        <span>
          <strong>API Key / Token</strong>
        </span>
        <button
          id="connector-form-edit-api-key"
          class="btn btn-link mb-0 py-0 px-2"
          type="button"
          @click="editApiToken"
        >
          <i class="fa fa-edit"/>
        </button>
      </label>
    </div>
    <div v-if="props.initialData" class="mb-3">
      <sh-switch
        id="is-active"
        v-model="formData.is_active"
        :checked="formData.is_active"
        title="Is Active"
      />
    </div>
    <div v-if="!props.initialData && testConnectionResult" class="mb-3">
      <div class="w-100">
        <sh-badge
          v-if="testConnectionResult.success"
          class="d-block text-start test-connection-result"
          variant="gradient"
          :label="testConnectionResult.message"
          icon="check"
        />
        <sh-badge
          v-else
          class="d-block text-start test-connection-result"
          variant="gradient"
          color="danger"
          :label="testConnectionResult.message"
          icon="times"
        />
      </div>
    </div>

    <hr class="mt-0"/>
    <div class="d-flex justify-content-end align-items-center">
      <div v-if="props.errors.form_error" class="text-danger me-2">
        {{ props.errors.form_error }}
      </div>
      <button
        v-if="!props.initialData"
        type="button"
        class="btn btn-outline-primary ms-2 mb-0"
        :disabled="isButtonDisabled"
        @click="testConnection"
      >
        <i
          v-if="isLoading"
          class="fa fa-spinner fa-spin me-1"
        />
        <i v-else class="fa fa-plug me-1"/>
        {{ isLoading ? 'Testing...' : 'Test Connection' }}
      </button>
      <button
        id="connector-form-cancel"
        class="btn btn-secondary ms-2 mb-0"
        type="button"
        @click="emit('cancel')"
      >
        Cancel
      </button>
      <button
        id="connector-form-submit"
        class="btn btn-primary ms-2 mb-0"
        type="submit"
      >
        Save
      </button>
    </div>
  </form>
</template>
<style scoped>
.test-connection-result {
  white-space: pre-wrap;
  word-break: break-word;
  max-width: 100%;
  width: auto;
}
</style>
