<script setup>
import { ref, onMounted, computed } from 'vue';
import ShInput from '@/components/generic/ShInput.vue';
import ShSwitch from "@/components/generic/ShSwitch.vue";
import ShBadge from "@/components/generic/ShBadge.vue";

const props = defineProps({
  errors: {
    type: Object,
    default: () => ({}),
  },
  initialData: {
    type: Object,
    default: null,
  },
  testConnectionResult: {
    type: Object,
    default: null,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['submitted', 'cancel', 'test-connection']);

const formData = ref({
  name: null,
  base_url: null,
  username: null,
  password: null,
  is_active: true,
  application: 'ansible',
});

const isButtonDisabled = computed(() => {
  return props.isLoading || !formData.value.base_url || !formData.value.username || !formData.value.api_key;
});

onMounted(() => {
  if (props.initialData) {
    formData.value = {
      name: props.initialData.name || null,
      base_url: props.initialData.base_url || null,
      username: props.initialData.username || null,
      password: props.initialData.password || null,
      application: props.initialData.application || 'ansible',
    };
  }
});

function testConnection() {
  emit('test-connection', formData.value);
}
</script>

<template>
  <form @submit.prevent="emit('submitted', formData)">
    <div class="mb-3">
      <sh-input
        id="connector-form-name"
        v-model="formData.name"
        type="text"
        placeholder="Enter connector name"
        title="Connector Name"
        :errors="props.errors.name"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="connector-url"
        v-model="formData.base_url"
        type="url"
        placeholder="E.g. https://awx.example.com"
        title="Base URL"
        :errors="props.errors.base_url"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="connector-username"
        v-model="formData.username"
        type="text"
        placeholder="Enter username"
        title="Username"
        :errors="props.errors.username"
      />
    </div>
    <div
      class="mb-3"
    >
      <sh-input
        id="connector-password"
        v-model="formData.api_key"
        type="password"
        placeholder="Enter password"
        title="Password"
        :errors="props.errors.api_key"
      />
    </div>
    <div v-if="props.initialData" class="mb-3">
      <sh-switch
        id="is-active"
        v-model="formData.is_active"
        :checked="formData.is_active"
        title="Is Active"
      />
    </div>
    <div v-if="!props.initialData && testConnectionResult" class="mb-3">
      <div class="w-100">
        <sh-badge
          v-if="testConnectionResult.success"
          class="d-block text-start test-connection-result"
          variant="gradient"
          icon="check"
          :label="testConnectionResult.message"
        />
        <sh-badge
          v-else
          class="d-block text-start test-connection-result"
          variant="gradient"
          color="danger"
          icon="times"
          :label="testConnectionResult.message"
        />
      </div>
    </div>
    <hr/>
    <div class="d-flex justify-content-end align-items-center">
      <div
        v-if="props.errors.form_error"
        class="text-danger me-2"
      >
        {{ props.errors.form_error }}
      </div>
      <button
        v-if="!props.initialData"
        type="button"
        class="btn btn-outline-primary ms-2 mb-0"
        :disabled="isButtonDisabled"
        @click="testConnection"
      >
        <i
          v-if="isLoading"
          class="fa fa-spinner fa-spin me-1"
          />
        <i v-else class="fa fa-plug me-1"/>
        {{ isLoading ? 'Testing...' : 'Test Connection' }}
      </button>
      <button
        id="connector-form-cancel"
        class="btn btn-secondary ms-2 mb-0"
        type="button"
        @click="emit('cancel')"
      >
        Cancel
      </button>
      <button
        id="connector-form-submit"
        class="btn btn-primary ms-2 mb-0"
        type="submit"
      >
        Save
      </button>
    </div>
  </form>
</template>
<style scoped>
.test-connection-result {
  white-space: pre-wrap;
  word-break: break-word;
  max-width: 100%;
  width: auto;
}
</style>
