<script setup>
import { onMounted, ref, watch } from 'vue';
import { fetchConnectors } from '@/api/connectors';
import { fetchRuleTypesForConnector } from '@/api/rules';
import ShInput from '@/components/generic/ShInput.vue';
import { FREQUENCIES, ALERT_SEVERITIES } from '@/utils/constants';
import ShSwitch from "@/components/generic/ShSwitch.vue";

const props = defineProps({
  errors: {
    type: Object,
    default: () => ({}),
  },
  initialData: {
    type: Object,
    default: null,
  },
  buttonTextSubmit: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['submitted', 'cancel']);

const formData = ref({
  description: null,
  connector: null,
  type: null,
  alert_severity: null,
  frequency_cron: null,
  frequency_name: null,
  is_active: true,
});

const connectors = ref([]);
const availableRuleTypes = ref([]);

const initialiseForm = async () => {
  await _fetchConnectors();
  if (props.initialData) {
    await _fetchRuleTypesForConnector(props.initialData.connector.id);
    formData.value.description = props.initialData.description;
    formData.value.connector = props.initialData.connector.id;
    formData.value.type = props.initialData.type;
    formData.value.alert_severity = props.initialData.alert_severity;
    formData.value.is_active = props.initialData.is_active;
    const frequency = FREQUENCIES.find(f => f.name === props.initialData.frequency_name);
    if (frequency) {
      formData.value.frequency_id = frequency.id;
    }
  }
  console.log('Form initialised with data:', formData.value);
};

const _fetchConnectors = async () => {
  const response = await fetchConnectors();
  if (response.status === 200) {
    connectors.value = response.data;
  }
};

const _fetchRuleTypesForConnector = async (connectorId) => {
  if (!connectorId) {
    availableRuleTypes.value = [];
    return;
  }

  const selectedConnector = connectors.value.find(c => c.id === parseInt(connectorId));
  if (!selectedConnector) {
    availableRuleTypes.value = [];
    return;
  }

  try {
    const response = await fetchRuleTypesForConnector(selectedConnector.application);
    if (response.status === 200 && response.data.success) {
      availableRuleTypes.value = response.data.rule_types;
    } else {
      availableRuleTypes.value = [];
    }
  } catch (error) {
    availableRuleTypes.value = [];
  }
};

const submitForm = (formData) => {
  const frequency = FREQUENCIES.find(f => f.id === formData.frequency_id);
  if (frequency) {
    formData.frequency_name = frequency.label;
    formData.frequency_cron = frequency.cron;
  }
  emit('submitted', formData);
};

watch(() => formData.value.connector, (newConnectorId, oldConnectorId) => {
  if (newConnectorId !== oldConnectorId) {
    _fetchRuleTypesForConnector(newConnectorId);
  }
});

onMounted(() => {
  initialiseForm();
});

</script>

<template>
  <form @submit.prevent="submitForm(formData)">
    <div class="mb-3">
      <sh-input
        id="rule-form-connector"
        v-model="formData.connector"
        name="connector"
        type="select"
        :options="connectors"
        :errors="errors.connector"
        title="Connector"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="rule-form-type"
        v-model="formData.type"
        name="type"
        type="select"
        :options="availableRuleTypes"
        :errors="errors.type"
        title="Rule Type"
        :placeholder="formData.connector ? '' : 'Select a connector first'"
        :disabled="!formData.connector"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="rule-form-alert-severity"
        v-model="formData.alert_severity"
        name="alert_severity"
        type="select"
        :options="ALERT_SEVERITIES"
        :errors="errors.alert_severity"
        title="Alert Severity"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="rule-form-description"
        v-model="formData.description"
        title="Description"
        placeholder="Enter description"
        aria-labelledby="description"
        type="text"
        :errors="errors.description"
      />
    </div>
    <div class="mb-3">
      <sh-input
        id="rule-form-frequency"
        v-model="formData.frequency_id"
        title="Frequency"
        aria-labelledby="frequency"
        type="select"
        :options="FREQUENCIES"
        :errors="errors.frequency_cron"
      />
    </div>
    <div v-if="initialData" class="mb-3">
      <sh-switch
        id="is-active"
        v-model="formData.is_active"
        :checked="formData.is_active"
        title="Is Active"
        name="is-active"
      />
    </div>
    <hr class="mt-0"/>
    <div>
      <div class="d-flex justify-content-end">
        <button
          id="rule-form-cancel-button"
          class="btn btn-secondary ms-2"
          type="button"
          @click="$emit('cancel')"
        >
          Cancel
        </button>
        <button
          id="rule-form-submit-button"
          class="btn btn-primary ms-2"
          type="submit"
        >
          {{ buttonTextSubmit }}
        </button>
      </div>
    </div>
  </form>
</template>
