<script setup>
import {
  defineProps,
  defineEmits,
} from 'vue';
import ShLoader from '@/components/generic/ShLoader.vue';
import Paginator from '@/components/lists/Paginator.vue';

const emit = defineEmits(['click-row', 'change-page', 'sort'])

defineProps({
  title: {
    type: String,
    default: null,
  },
  icon: {
    type: String,
    required: false,
    default: null,
  },
  subTitle: {
    type: String,
    required: false,
    default: null,
  },
  headers: {
    type: Array,
    required: false,
    default: null,
  },
  objectList: {
    type: Array,
    required: false,
    default: null,
  },
  pageData: {
    type: Object,
    required: false,
    default: null,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  loadingText: {
    type: String,
    default: null,
  },
  emptyText: {
    type: String,
    default: null,
  },
  isRowClickable: {
    type: Boolean,
    default: false,
  },
  currentSorting: {
    type: String,
    default: '',
  },
})

const handleHeaderClick = (sortField) => {
  if (!sortField) return;

  emit('sort', sortField);
};
</script>

<template>
  <div class="card">
    <div class="card-header d-flex justify-content-between">
      <div class="d-flex align-items-center">
        <h6
          v-if="title"
          class="m-0"
        >
          <i
            v-if="icon"
            class="fa fa-line-chart me-1"
            :class="`fa-${icon}`"
          />
          {{ title }}
        </h6>
      </div>
      <slot name="actions"/>
    </div>
    <div class="table-responsive">
      <table
        class="table table-generic align-items-center mb-0"
        :class="{'table-hover': isRowClickable}"
      >
        <thead>
        <tr v-if="!headers">
          <slot name="headers"/>
        </tr>
        <tr v-else>
          <th
            v-for="(header, index) in headers"
            :key="index"
            class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7"
            :class="{ 'clickable': header.sortField }"
            @click="handleHeaderClick(header.sortField)"
          >
            <span v-if="header.label">
              {{ header.label }}
              <i
                v-if="currentSorting === header.sortField"
                class="fa fa-sort-asc ms-1"
              />
              <i
                v-else-if="currentSorting === `-${header.sortField}`"
                class="fa fa-sort-desc ms-1"
              />
              <i
                v-else-if="header.sortField"
                class="fa fa-sort ms-1"
              />
            </span>
          </th>
        </tr>
        </thead>
        <tbody
          v-if="isLoading"
          class="generic-table-body"
        >
          <tr>
            <td
              :colspan="headers ? headers.length : 1"
              class="text-center"
            >
              <sh-loader
                :text="loadingText"
              />
            </td>
          </tr>
        </tbody>
        <tbody
          v-else-if="!objectList || objectList.length === 0"
          class="generic-table-body"
        >
          <tr>
            <td
              :colspan="headers ? headers.length : 1"
              class="text-center"
            >
              <p class="text-muted my-2">
                {{ emptyText || 'No data to display.' }}
              </p>
            </td>
          </tr>
        </tbody>
        <tbody
          v-else-if="isRowClickable"
          class="generic-table-body"
        >
        <tr
          v-for="(object, index) in objectList"
          :key="index"
          class="clickable"
          @click="emit('click-row', object)"
        >
          <slot
            name="rows"
            :object="object"
          />
        </tr>
        </tbody>
        <tbody
          v-else
          class="generic-table-body"
        >
        <tr
          v-for="(object, index) in objectList"
          :key="index"
        >
          <slot
            name="rows"
            :object="object"
          />
        </tr>
        </tbody>
      </table>
    </div>
    <div
      v-if="pageData"
      class="card-footer d-flex justify-content-end"
    >
      <paginator
        :page-data="pageData"
        @change="(pageNumber) => emit('change-page', pageNumber)"
      />
    </div>
  </div>
</template>

<style>
.generic-table-body td {
  padding: 0.75rem 1.5rem;
}
</style>
