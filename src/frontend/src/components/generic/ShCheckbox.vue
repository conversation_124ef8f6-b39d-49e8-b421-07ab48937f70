<script setup>
const emit = defineEmits(['update:modelValue'])
defineProps({
  name: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
  checked: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  }
})
</script>
<template>
  <div class="form-check">
    <span v-if="title">
        <strong>
          {{ title }}
        </strong>
      </span>
    <input
      :id="id"
      class="form-check-input"
      type="checkbox"
      :name="name"
      :checked="checked"
      @change="emit('update:modelValue', $event.target.checked)"
    />
  </div>
</template>
