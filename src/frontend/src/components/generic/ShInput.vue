<script setup>
const emit = defineEmits(['update:modelValue'])

defineProps({
  size: {
    type: String,
    default: 'default',
  },
  success: {
    type: Boolean,
    default: false,
  },
  error: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: '',
  },
  iconDir: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, Number],
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text',
  },
  isRequired: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  errors: {
    type: Object,
    default: () => null,
  },
  options: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const getClasses = (size, success, error) => {
  let sizeValue = size ? `form-control-${size}` : '';
  let isValidValue = error ? 'is-invalid' : success ? 'is-valid' : '';
  return `${sizeValue} ${isValidValue}`;
}

const getIcon = (icon) => (icon ? icon : null)
const hasIcon = (icon) => (icon ? 'input-group' : null)
</script>

<template>
  <div class="form-group">
    <div :class="hasIcon(icon)">
      <span v-if="iconDir === 'left'" class="input-group-text">
        <i :class="getIcon(icon)"></i>
      </span>

      <label
        v-if="title"
        :for="id"
        class="w-100 mb-1"
      >
        <strong>{{ title }}</strong>
      </label>

      <template v-if="type === 'select'">
        <select
          :id="id"
          :value="modelValue"
          :name="name"
          class="form-select"
          :required="isRequired"
          :disabled="disabled"
          @input="emit('update:modelValue', $event.target.value)"
        >
          <option selected disabled value="">{{ placeholder }}</option>
          <option
            v-for="option in options"
            :key="option.id"
            :value="option.id"
            :disabled="option.disabled"
          >
            {{ option.name }}
          </option>
        </select>
      </template>

      <template v-else>
        <input
          :id="id"
          :type="type"
          class="form-control"
          :class="getClasses(size, success, error)"
          :name="name"
          :placeholder="placeholder"
          :required="isRequired"
          :disabled="disabled"
          :value="modelValue"
          @input="emit('update:modelValue', $event.target.value)"
        />
      </template>

      <span v-if="iconDir === 'right'" class="input-group-text">
        <i :class="getIcon(icon)"></i>
      </span>
    </div>

    <div
      v-if="errors"
      :id="`${id}-error`"
      class="text-danger"
    >
      {{ errors }}
    </div>
  </div>
</template>