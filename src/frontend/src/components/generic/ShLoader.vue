<script setup>
defineProps({
  text: {
    type: String,
    default: '',
  },
  iconSize: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="h-full d-flex justify-content-center align-items-center">
    <p
      v-if="iconSize === ''"
      class="text-muted my-2"
    >
      <i :class="['fa fa-spinner fa-spin', iconSize]"/>
      {{ text }}
    </p>
    <div v-else-if="iconSize === 'fa-2x'">
      <i :class="['fa fa-spinner fa-spin', iconSize]"/>
      <p
        v-if="text !== ''"
        class="text-muted"
      >
        {{ text }}
      </p>
    </div>
  </div>
</template>