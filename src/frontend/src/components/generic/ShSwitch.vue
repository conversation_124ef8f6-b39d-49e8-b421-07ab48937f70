<script setup>
const emit = defineEmits(['update:modelValue'])
defineProps({
  id: {
    type: String,
    required: true,
  },
  checked: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '',
  }
})
</script>
<template>
  <div class="form-check form-switch ms-1 ps-0">
    <label
      v-if="title"
      :for="id"
      class="m-0"
    >
      <strong>
        {{ title }}
      </strong>
    </label>
    <input
      :id="id"
      class="form-check-input ms-2 my-1"
      type="checkbox"
      :checked="checked"
      @change="emit('update:modelValue', $event.target.checked)"/>
  </div>
</template>