<script setup>
import { computed } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: 'md',
  },
  color: {
    type: String,
    default: 'success',
  },
  variant: {
    type: String,
    default: 'fill',
  },
  circular: {
    type: Boolean,
    default: false,
  },
  floating: {
    type: Boolean,
    default: false,
  },
  fullWidth: {
    type: Boolean,
    default: false,
  },
})


const badgeClasses = computed(() => {
  return {
    [`bg-gradient-${props.color}`]: props.variant === 'gradient',
    [`badge-${props.color}`]: props.variant !== 'gradient',
    [`badge-${props.size}`]: props.size,
    'badge-circle': props.circular,
    'badge-floating': props.floating,
    'w-100': props.fullWidth,
  }
});
</script>
<template>
  <span
    class="badge"
    :class="badgeClasses"
  >
    {{ label }}
  </span>
</template>
