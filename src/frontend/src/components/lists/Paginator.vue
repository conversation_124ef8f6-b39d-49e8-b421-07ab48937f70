<script setup>

defineEmits(['change'])

const props = defineProps({
  pageData: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <ul
    v-if="props.pageData.num_pages > 1"
    class="pagination pagination-success m-0"
  >
    <li class="m-0 page-item">
      <button
        class="m-0 page-link"
        :class="{ 'disabled': !props.pageData.previous }"
        :disabled="!props.pageData.previous"
        @click="$emit('change', props.pageData.previous)"
      >
        <i class="fa fa-angle-left"></i>
      </button>
    </li>
    <li class="page-item ms-2 me-2">
      <div class="h-100 d-flex align-items-center justify-content-center">
        <div class="badge rounded-5 bg-info">
          Page {{ props.pageData.current }} /
          {{ props.pageData.num_pages }}
        </div>
      </div>
    </li>
    <li class="m-0 page-item">
      <button
        class="m-0 page-link"
        :class="{ 'disabled': !props.pageData.next }"
        :disabled="!props.pageData.next"
        @click="$emit('change', props.pageData.next)"
      >
        <i class="fa fa-angle-right"></i>
      </button>
    </li>
  </ul>
</template>
