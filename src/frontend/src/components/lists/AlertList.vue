<script setup>
import ShInput from '@/components/generic/ShInput.vue';
import ShButton from '@/components/generic/ShButton.vue';
import ShLoader from '@/components/generic/ShLoader.vue';
import { updateAlert, fetchAlerts } from '@/api/alerts';
import { fetchUsers } from '@/api/users';

import {
  onMounted,
  ref,
  watch,
} from 'vue';
import ShBadge from '@/components/generic/ShBadge.vue';
import Paginator from '@/components/lists/Paginator.vue';
import { stringToPrettyDateTime } from '@/utils/datetime';
import { toggleSort } from '@/services/lists';
import ShAlert from '@/components/generic/ShAlert.vue';
import {
  SEVERITY_BADGE_CLASSES,
  STATUS_BADGE_CLASSES,
} from '@/utils/constants';

const props = defineProps({
  severities: {
    type: Array,
    default: null,
  },
  statuses: {
    type: Array,
    default: null,
  },
  assignee: {
    type: String,
    default: null,
  },
});

const users = ref([]);
const selectedUserId = ref(null);
const alerts = ref(null);
const search = ref('');
const showDialog = ref(false);
const showConfirm = ref(false);
const confirmMessage = ref('');
const ordering = ref('');
const loading = ref(true);


const closeShowConfirm = () => {
  showConfirm.value = false;
};

const _fetchUsers = async () => {
  const response = await fetchUsers();
  if (response.status === 200) {
    users.value = response.data;
  } else {
    console.log('Error fetching users:', response);
  }
};


const _updateAlert = async (alertId, updateData) => {
  const response = await updateAlert(alertId, updateData);
  if (response.status === 200) {
    showDialog.value = false;
    showConfirm.value = true;
    confirmMessage.value = `Alert ${alertId} has been updated successfully.`;
  } else {
    console.error('Error updating alert status:', response.data);
  }
}



const loadAlerts = async (pageNumber) => {
  const response = await fetchAlerts({
    search: search.value,
    severities: props.severities,
    statuses: props.statuses,
    assignee: props.assignee,
    ordering: ordering.value,
    pageNumber,
  });
  if (response.status === 200) {
    alerts.value = response.data;
    loading.value = false;
  } else {
    alert('Something went wrong. Fetching alerts failed.');
  }
};

watch(() => props.statuses, () => loadAlerts(), { deep: true });
watch(showConfirm, (newValue) => {
  if (newValue) {
    loadAlerts()
  }
}, { immediate: true });

onMounted(async () => {
    await loadAlerts();
    await _fetchUsers();
  },
)

</script>

<template>
  <div class="card mb-4">
    <div class="card-header pb-0">
      <form
        class="d-flex justify-content-end"
        @submit.prevent="loadAlerts(page)"
      >
        <slot name="tabs"/>
        <div class="me-2">
          <sh-input
            id="alert-search-input"
            v-model="search"
            placeholder="Search..."
            type="text"
            class="input-group-alternative"
          />
        </div>
        <div>
          <sh-button
            id="alert-search-submit"
            variant="gradient"
            color="secondary"
            type="submit"
          >
            <i class="fa fa-search"></i>
          </sh-button>
          <sh-button
            v-if="search"
            id="alert-clear-search"
            class="ms-2"
            variant="gradient"
            color="danger"
            type="button"
            @click="search = ''; loadAlerts()"
          >
            <i class="fa fa-times"></i>
          </sh-button>
        </div>
      </form>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive p-0">
        <table class="table align-items-center justify-content-center mb-0">
          <thead>
          <tr>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'message', loadAlerts)"
            >
              Message
              <i v-if="ordering === 'message'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-message'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'rule', loadAlerts)"
            >
              Rule
              <i v-if="ordering === 'rule'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-rule'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'rule__connector', loadAlerts)"
            >
              Connector
              <i v-if="ordering === 'rule__connector'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-rule__connector'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'created_at', loadAlerts)"
            >
              Date
              <i v-if="ordering === 'created_at'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-created_at'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>

            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'assignee__username', loadAlerts)"
            >
              Assignee
              <i v-if="ordering === 'assignee__username'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-assignee__username'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'status', loadAlerts)"
            >
              Status
              <i v-if="ordering === 'status'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-status'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder clickable"
              @click="() => toggleSort(ordering, 'severity', loadAlerts)"
            >
              Severity
              <i v-if="ordering === 'severity'" class="fa fa-sort-asc ms-1"/>
              <i v-else-if="ordering === '-severity'" class="fa fa-sort-desc ms-1"/>
              <i v-else class="fa fa-sort ms-1"/>
            </th>
          </tr>
          </thead>
          <tbody v-if="loading">
          <tr>
            <td colspan="7" class="text-center">
              <sh-loader
                text="Loading alerts..."
              />
            </td>
          </tr>
          </tbody>
          <tbody v-else-if="alerts.results.length === 0">
          <tr>
            <td colspan="7" class="text-center">
              <p class="text-muted my-2">
                No alerts available
              </p>
            </td>
          </tr>
          </tbody>
          <tbody v-else-if="alerts.results.length > 0">
          <router-link
            v-for="alert in alerts.results"
            v-slot="{ navigate }"
            :key="alert.id"
            :to="{ name: 'alert-detail', params: { id: alert.id } }"
            custom
          >
            <tr
              :id="`alert-list-row-${alert.id}`"
              class="clickable"
              role="button"
              @click="navigate"
            >
              <td class="ps-3">
                <div class="d-flex align-items-center">
                  <h6 class="mb-0 text-sm alert-col me-2">
                    {{ alert.message }}
                  </h6>
                  <sh-badge
                    v-if="alert.recreated_count"
                    :color="alert.recreated_count ? 'danger' : 'secondary'"
                    :label="alert.recreated_count + 1"
                  />
                </div>
              </td>

              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  {{ alert.rule.description }}
                </h6>
              </td>
              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  {{ alert.rule.connector.name }}
                </h6>
              </td>
              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  {{ stringToPrettyDateTime(alert.created_at) }}
                </h6>
              </td>
              <td class="ps-3">
                <h6
                  v-if="alert.assignee"
                  class="mb-0 text-sm"
                >
                  {{ alert.assignee.username }}
                </h6>
                <div v-else-if="props.statuses.includes('initial') && !alert.assignee">
                  <select
                    id="alert-assignee-select"
                    v-model="selectedUserId"
                    class="form-select-sm"
                    @click.stop
                    @change="_updateAlert(alert.id, { assignee: selectedUserId }); selectedUserId = null"
                  >
                    <option selected disabled>-</option>
                    <option
                      v-for="user in users"
                      :key="user.id"
                      :value="user.id"
                    >
                      {{ user.username }}
                    </option>
                  </select>
                </div>
              </td>
              <td class="ps-3">
                <sh-badge
                  :label="alert.status"
                  :color="STATUS_BADGE_CLASSES[alert.status]"
                  :full-width="true"
                />
              </td>
              <td class="ps-3">
                <sh-badge
                  :label="alert.severity"
                  :color="SEVERITY_BADGE_CLASSES[alert.severity]"
                  :full-width="true"
                />
              </td>
            </tr>
          </router-link>
          </tbody>
        </table>
      </div>
    </div>
    <div
      v-if="alerts && alerts.results.length > 0 && !loading"
      class="card-footer d-flex justify-content-end mb-0"
    >
      <paginator
        :page-data="alerts.page"
        @change="loadAlerts"
      />
    </div>
  </div>
  <sh-alert
    v-if="showConfirm"
    title="Success"
    :text="confirmMessage"
    icon="success"
    confirm-button-text="OK"
    @confirmed="closeShowConfirm"

  />
</template>

<style scoped>
.alert-col {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>