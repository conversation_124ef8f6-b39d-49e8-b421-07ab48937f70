<script setup>
import ShBadge from "@/components/generic/ShBadge.vue";

defineProps({
  name: {
    type: String,
    required: true,
  },
  application: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    required: true,
  },
  account: {
    type: String,
    required: true,
  },
  username: {
    type: String,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
  lastSyncStatus: {
    type: Object,
    default: null,
  },
});

const getApplicationLogo = (application) => {
  try {
    return require(`@/assets/images/applications/${application}.png`);
  } catch (e) {
    return require('@/assets/images/applications/conjur.png');
  }
};

const connectorSyncStatusMap = {
  success: 'text-success',
  failed: 'text-danger',
  initial: 'text-warning',
  in_progress: 'text-info',
};

</script>


<template>
  <div class="card card-connector h-100 clickable">
    <div class="p-3 pb-0 card-header">
      <div class="row">
        <div class="col-md-8 d-flex align-items-center">
          <h4 class="mb-0">{{ name }}</h4>
        </div>
        <div class="col-md-4 text-end">
          <sh-badge
            v-if="active"
            variant="gradient"
            size="sm"
            color="success"
            class="me-3"
          >
            Active
          </sh-badge>
          <sh-badge
            v-else
            variant="gradient"
            size="sm"
            color="danger"
            class="me-3"
          >
            Inactive
          </sh-badge>
          <i
            v-if="lastSyncStatus"
            :class="connectorSyncStatusMap[lastSyncStatus]"
            class="text-sm fas fa-sync me-2"
            :title="`Last sync status: ${lastSyncStatus}`"
          />
          <i
            v-else
            class="text-sm fas fa-sync me-2 text-secondary"
            title="No sync runs yet"
          />
        </div>
      </div>
    </div>
    <div class="p-3 pt-0 card-body">
      <hr class="my-3 horizontal dark"/>
      <div class="row justify-content-center">
        <img
          :src="getApplicationLogo(application)"
          :alt="`${application} logo`"
          class="w-75"
        />
      </div>
    </div>
  </div>
</template>


<style scoped>
.card-connector {
  transition: all 0.2s ease-in-out;
}

.card-connector:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
</style>
