import api from './base';

export const createConnector = (data) => {
  return api.post('connectors/', data);
};

export const deleteConnector = (connectorSlug) => {
  return api.delete(`connectors/${connectorSlug}/`);
};

export const fetchConnector = (connectorId) => {
  return api.get(`connectors/${connectorId}/`);
};

export const fetchConnectorStatistics = (connectorId) => {
  return api.get(`connectors/${connectorId}/statistics/`);
};

export const fetchConnectors = () => {
  return api.get('connectors/');
};

export const startConnectorSync = (connectorSlug) => {
  return api.post(`connectors/${connectorSlug}/sync/`);
};

export const testConnectorConnection = (data) => {
  return api.post('connectors/test-connection/', data);
};

export const updateConnector = (connectorId, data) => {
  return api.put(`connectors/${connectorId}/`, data);
};
