import api from './base';

export const fetchSecretProfiles = (options = {}) => {
  const { search, createdAtFrom, ordering, pageNumber } = options;
  const params = {};
  if (search) {
    params.search = search;
  }
  if (createdAtFrom) {
    params.created_at_from = createdAtFrom;
  }
  if (ordering) {
    params.ordering = ordering;
  }
  if (pageNumber) {
    params.page = pageNumber;
  }

  return api.get('secret-profiles/', params);
};
