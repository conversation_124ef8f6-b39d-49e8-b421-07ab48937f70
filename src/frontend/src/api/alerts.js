import api from './base';

export const fetchAlert = (alertId) => {
  return api.get(`alerts/${alertId}/`);
};

export const fetchAlertCharData = () => {
  return api.get('alerts/timeseries/');
};

export const fetchAlerts = (options = {}) => {
  const { search, severities, statuses, assignee, ordering, pageNumber } = options;
  const params = {};
  if (search) {
    params.search = search;
  }
  if (Array.isArray(severities) && severities.length > 0) {
    params.severities = severities.join(',');
  }
  if (Array.isArray(statuses) && statuses.length > 0) {
    params.statuses = statuses.join(',');
  }
  if (assignee) {
    params.assignee = assignee;
  }
  if (pageNumber) {
    params.page = pageNumber;
  }
  if (ordering) {
    params.ordering = ordering;
  }
  return api.get('alerts/', params);
};

export const fetchMostAlertsPerConnector = () => {
  return api.get('connectors/alerts-per-connector/');
};

export const updateAlert = (selectedAlertId, data) => {
  return api.put(`alerts/${selectedAlertId}/`, data);
};
