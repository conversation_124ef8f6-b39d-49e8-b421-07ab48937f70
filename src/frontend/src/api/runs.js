import api from './base';

export const fetchRuleRuns = (options = {}) => {
  const { search, ruleId, pageNumber } = options;
  const params = {};
  if (search) {
    params.search = search;
  }
  if (ruleId) {
    params.rule_id = ruleId;
  }
  if (pageNumber) {
    params.page = pageNumber;
  }
  return api.get('rule-runs/', params);
};

export const fetchSyncRuns = (options) => {
  const { connectorId, search, pageNumber } = options;
  const params = {};
  if (connectorId) {
    params.connector_id = connectorId;
  }
  if (search) {
    params.search = search;
  }
  if (pageNumber) {
    params.page = pageNumber;
  }

  return api.get('sync-runs/', params);
};
