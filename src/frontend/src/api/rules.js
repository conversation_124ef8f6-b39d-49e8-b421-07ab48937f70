import api from './base';

export const createRule = (formData) => {
  return api.post('rules/', formData);
};

export const deleteRule = (ruleId) => {
  return api.delete(`rules/${ruleId}/`);
};

export const fetchRule = (ruleId) => {
  return api.get(`rules/${ruleId}/`);
};

export const fetchRuleTypesForConnector = (application) => {
  const params = { application };
  return api.get('connectors/rule-types/', params);
};

export const fetchRules = (options = {}) => {
  const { page, limit } = options;
  const params = {};
  if (page) {
    params.page = page;
  }
  if (limit) {
    params.limit = limit;
  }
  return api.get('rules/', params);
};

export const runRule = (ruleId) => {
  return api.post(`rules/${ruleId}/run/`);
};

export const updateRule = (ruleId, data) => {
  return api.put(`rules/${ruleId}/`, data);
};
