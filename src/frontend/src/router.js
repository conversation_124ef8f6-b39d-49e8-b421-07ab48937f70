import { createRouter, createWebHistory } from "vue-router";

const routes = [
  {
    path: "/",
    name: "dashboard",
    component: () => import('@/views/core/DashboardView.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/LoginView.vue'),
  },
  {
    path: '/rules',
    name: 'rule-list',
    component: () => import('@/views/rules/RuleListView.vue'),
  },
  {
    path: '/rules/create',
    name: 'rule-create',
    component: () => import('@/views/rules/RuleCreateView.vue'),
  },
  {
    path: '/alerts/:id',
    name: 'alert-detail',
    component: () => import('@/views/alerts/AlertDetailView.vue'),
  },
  {
    path: '/alerts',
    name: 'alerts',
    component: () => import('@/views/alerts/AlertListView.vue'),
  },
  {
    path: '/connectors',
    name: 'connector-list',
    component: () => import('@/views/connectors/ConnectorListView.vue'),
  },
  {
    path: '/connectors/:id',
    name: 'connector-detail',
    component: () => import('@/views/connectors/ConnectorDetailView.vue'),
  },
  {
    path: '/connectors/create',
    name: 'connector-create',
    component: () => import('@/views/connectors/ConnectorCreateView.vue'),
  },
  {
    path: '/connectors/update/:id',
    name: 'connector-update',
    component: () => import('@/views/connectors/ConnectorUpdateView.vue'),
  },
  {
    path: '/connectors/sync-runs',
    name: 'connectors-sync-runs',
    component: () => import('@/views/runs/SyncRunListView.vue'),
  },
  {
    path: '/rules/:id',
    name: 'rule-detail',
    component: () => import('@/views/rules/RuleDetailView.vue'),
  },
  {
    path: '/rules/update/:id',
    name: 'rule-update',
    component: () => import('@/views/rules/RuleUpdateView.vue'),
  },
  {
    path: '/secrets',
    name: 'secret-list',
    component: () => import('@/views/secretProfiles/SecretProfileListView.vue'),
  },
  {
    path: '/profile',
    name: 'user-profile',
    component: () => import('@/views/users/UserProfileView.vue'),
  },
  {
    path: '/logout',
    name: 'user-logout',
    component: () => import('@/views/auth/UserLogout.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/core/NotfoundView.vue'),
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  linkActiveClass: "active"
});

export default router;
