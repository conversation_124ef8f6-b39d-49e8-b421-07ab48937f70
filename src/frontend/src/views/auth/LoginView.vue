<script setup>
import {
  ref,
  onBeforeMount,
  onBeforeUnmount,
} from 'vue';
import { useStore } from 'vuex';
import { getToken } from '@/api/auth';
import { useRouter } from 'vue-router'
import { watch } from 'vue';

import ShInput from '@/components/generic/ShInput.vue';
import ShButton from '@/components/generic/ShButton.vue';

const body = document.getElementsByTagName('body')[0];
const store = useStore();
const router = useRouter();

const toggleDefaultLayout = () => store.commit('toggleDefaultLayout');


onBeforeMount(() => {
  store.state.hideConfigButton = true;
  toggleDefaultLayout();
  body.classList.remove('bg-gray-100');
});
onBeforeUnmount(() => {
  store.state.hideConfigButton = false;
  toggleDefaultLayout();
  body.classList.add('bg-gray-100');
});

const formData = {
  username: '',
  password: '',
};

const loginError = ref('');

const login = async () => {
  loginError.value = '';
  try {
    const response = await getToken({
      username: formData.username,
      password: formData.password,
    });
    localStorage.setItem('token', response.data.token);
    watch(
      () => store.state.user,
      (newValue) => {
        if (newValue) {
          router.push({ name: 'dashboard' });
        }
      },
      { immediate: true },
    )
  } catch (error) {
    if (error.response && error.response.status === 400) {
      loginError.value = error.response.data['non_field_errors'][0];
    }
    console.error('Login mislukt:', error);
  }
};

</script>
<template>
  <main class="mt-0 main-content">
    <section>
      <div class="page-header min-vh-100">
        <div class="container">
          <div class="row">
            <div
              class="mx-auto col-xl-4 col-lg-5 col-md-7 d-flex flex-column mx-lg-0"
            >
              <div class="card card-plain">
                <div class="pb-0 card-header text-start">
                  <h4 class="font-weight-bolder">Sign In</h4>
                  <p class="mb-0">Enter your email and password to sign in</p>
                </div>
                <div class="card-body">
                  <form role="form" @submit.prevent="login">
                    <div class="mb-3">
                      <sh-input
                        id="email"
                        v-model="formData.username"
                        type="email"
                        placeholder="Email"
                        name="email"
                        size="lg"
                      />
                    </div>
                    <div>
                      <sh-input
                        id="password"
                        v-model="formData.password"
                        type="password"
                        placeholder="Password"
                        name="password"
                        size="lg"
                      />
                      <p v-if="loginError" class="text-danger">
                        {{ loginError }}
                      </p>
                    </div>
                    <div class="text-center">
                      <sh-button
                        id="button-login"
                        variant="gradient"
                        color="primary"
                        full-width
                        size="lg"
                        type="submit"
                      >
                        Sign in
                      </sh-button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
            <div
              class="col-6 d-lg-flex d-none h-100 my-auto pe-0 position-absolute top-0 end-0 text-center justify-content-center flex-column"
            >
              <div
                class="position-relative h-100 m-3 px-7 border-radius-lg d-flex flex-column justify-content-center overflow-hidden"
                :style="{
                  backgroundImage:
                    'url(' +
                    'https://raw.githubusercontent.com/creativetimofficial/public-assets/master/argon-dashboard-pro/assets/img/signin-ill.jpg' +
                    ')',
                  backgroundSize: 'cover'
                }"
              >
                <span class="mask bg-gradient-success opacity-6"></span>

                <h4
                  class="mt-5 text-white font-weight-bolder position-relative"
                >
                  CISolutions
                </h4>
                <p class="text-white position-relative">
                  "Building trust through security"
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>
