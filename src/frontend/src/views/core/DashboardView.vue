<script setup>
import { useStore } from "vuex";
import MiniStatisticsCard from "@/components/visualizations/MiniStatisticsCard.vue";
import GradientLineChart from "@/components/visualizations/GradientLineChart.vue";
import Bar<PERSON><PERSON> from "@/components/visualizations/BarChart.vue";
import { onMounted, ref, watch } from "vue";
import { fetchAlertCharData, fetchAlerts, fetchMostAlertsPerConnector } from "@/api/alerts";
import { fetchSecretProfiles } from "@/api/secretProfiles";
import ShLoader from "@/components/generic/ShLoader.vue";
import ShTable from '@/components/generic/ShTable.vue';
import ShBadge from '@/components/generic/ShBadge.vue';
import { stringToPrettyDateTime } from '@/utils/datetime';
import { toggleSort } from '@/services/lists';
import {
  SEVERITY_BADGE_CLASSES,
  STATUS_BADGE_CLASSES,
} from '@/utils/constants';
import { useRouter } from 'vue-router';

const barChartData = ref(null);
const lineChartData = ref(null);
const store = useStore();
const router = useRouter();
const criticalAlertsCount = ref(0);
const criticalAlerts = ref([]);
const criticalAlertsPageData = ref({});
const criticalAlertsOrdering = ref('');
const totalSecretsProfiles = ref(0);
const recentSecretProfiles = ref(0);
const assignedAlerts = ref(0);

const isLoadingCriticalAlertsList = ref(true);
const isLoadingLineChart = ref(true);
const isLoadingBarChart = ref(true);
const isLoadingCriticalAlertsCard = ref(true);
const isLoadingTotalSecretsCard = ref(true);
const isLoadingRecentSecretProfilesCard = ref(true);
const isLoadingAssignedAlertsCard = ref(true);

store.commit("setBreadcrumb", {
  currentDirectory: "Dashboard",
});

const CRITICAL_ALERTS_HEADERS = [
  { label: 'Message', sortField: 'message' },
  { label: 'Rule', sortField: 'rule' },
  { label: 'Connector', sortField: 'rule__connector' },
  { label: 'Date', sortField: 'created_at' },
  { label: 'Status', sortField: 'status' },
  { label: 'Severity', sortField: 'severity' },
];

const _fetchAlertCharData = async () => {
  const response = await fetchAlertCharData()
  if (response.status === 200) {
    lineChartData.value = response.data;
    isLoadingLineChart.value = false;
  } else {
    console.error("Error fetching alert chart data:", response.data);
  }
}

const _fetchMostAlertsPerConnector = async () => {
  const response = await fetchMostAlertsPerConnector()
  if (response.status === 200) {
    barChartData.value = response.data;
    isLoadingBarChart.value = false;
  } else {
    console.error("Error fetching most alerts per connector:", response.data);
  }
}

const getAssignedAlerts = async () => {
  const response = await fetchAlerts({
    assignee: store.state.user.id,
    statuses: ["initial"],
  })
  if (response.status === 200) {
    assignedAlerts.value = response.data.page.count;
    isLoadingAssignedAlertsCard.value = false;
  } else {
    console.error("Error fetching assigned alerts:", response.data);
  }
}

const getCriticalAlerts = async () => {
  const response = await fetchAlerts({
    severities: ["critical"],
    statuses: ["initial"],
  })
  if (response.status === 200) {
    criticalAlertsCount.value = response.data.page.count;
    isLoadingCriticalAlertsCard.value = false;
  } else {
    console.error("Error fetching critical alerts:", response.data);
  }
}

const loadCriticalAlerts = async (pageNumber) => {
  const response = await fetchAlerts({
    severities: ["critical"],
    statuses: ["initial"],
    ordering: criticalAlertsOrdering.value,
    pageNumber,
  });
  if (response.status === 200) {
    criticalAlerts.value = response.data.results;
    criticalAlertsPageData.value = response.data.page;
    isLoadingCriticalAlertsList.value = false;
  } else {
    console.error("Error fetching critical alerts list:", response.data);
    isLoadingCriticalAlertsList.value = false;
  }
};

const handleRowClick = (alert) => {
  router.push({ name: 'alert-detail', params: { id: alert.id } });
};



const getTotalSecretsProfiles = async () => {
  const response = await fetchSecretProfiles()
  if (response.status === 200) {
    totalSecretsProfiles.value = response.data.page.count;
    isLoadingTotalSecretsCard.value = false;
  } else {
    console.error("Error fetching total secrets profiles:", response.data);
  }
}

const getRecentSecretProfiles = async () => {
  const response = await fetchSecretProfiles({
    createdAtFrom: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
  })
  if (response.status === 200) {
    recentSecretProfiles.value = response.data.page.count;
    isLoadingRecentSecretProfilesCard.value = false;
  } else {
    console.error("Error fetching recent secret profiles:", response.data);
  }
}

watch(
  () => store.state.user,
  (user) => {
    if (user && user.id) {
      getAssignedAlerts();
    }
  },
  { immediate: true }
)

onMounted(
  () => {
    getCriticalAlerts();
    getTotalSecretsProfiles();
    getRecentSecretProfiles();
    _fetchAlertCharData();
    _fetchMostAlertsPerConnector();
    loadCriticalAlerts();
  }
);

</script>
<template>
  <div class="pb-4">
    <div class="row">
      <div class="col-lg-12">
        <div class="row gy-3 mb-3">
          <div class="col-lg-3 col-md-6 col-12">
            <mini-statistics-card
              :loading="isLoadingCriticalAlertsCard"
              title="Total Critical Alerts"
              :value="criticalAlertsCount"
              :icon="{
                component: 'ni ni-notification-70',
                background: 'bg-gradient-danger',
                shape: 'rounded-circle',
              }"
            />
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <mini-statistics-card
              :loading="isLoadingTotalSecretsCard"
              title="Total Secrets"
              :value="totalSecretsProfiles"
              :icon="{
                component: 'ni ni-key-25',
                background: 'bg-gradient-info',
                shape: 'rounded-circle',
              }"
            />
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <mini-statistics-card
              :loading="isLoadingRecentSecretProfilesCard"
              title="Total new Secrets (48h)"
              :value="recentSecretProfiles"
              :icon="{
                component: 'ni ni-key-25',
                background: 'bg-gradient-info',
                shape: 'rounded-circle',
              }"
            />
          </div>
          <div class="col-lg-3 col-md-6 col-12">
            <mini-statistics-card
              :loading="isLoadingAssignedAlertsCard"
              title="My tasks"
              :value="assignedAlerts"
              :icon="{
                component: 'ni ni-button-play',
                background: 'bg-gradient-success',
                shape: 'rounded-circle',
              }"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div
              v-if="isLoadingLineChart"
              class="card z-index-2"
            >
              <div class="pb-0 pt-3 card-header mb-0">
                <h6>Alerts over time</h6>
              </div>
              <div class="p-3 card-body">
                <div class="chart">
                  <div
                    id="chart-line-alerts"
                    class="chart-canvas"
                    style="height: 338px"
                  >
                    <div class="d-flex justify-content-center align-items-center h-100">
                      <div class="justify-content-between text-center">
                        <sh-loader
                          text="Loading..."
                          icon-size="fa-2x"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <gradient-line-chart
              v-if="lineChartData && !isLoadingLineChart"
              id="chart-line-alerts"
              title="Alerts over time"
              height="338"
              :chart="{
                labels: lineChartData.labels,
                datasets: [
                  {
                    label: lineChartData.datasets[0].label,
                    data: lineChartData.datasets[0].data,
                  },
                ],
              }"
            />
            <div
              v-else-if="!isLoadingLineChart && !lineChartData"
              class="card z-index-2"
            >
              <div class="pb-0 pt-3 card-header mb-0">
                <h6>Alerts over time</h6>
              </div>
              <div class="p-3 card-body">
                <div class="chart">
                  <div
                    id="chart-line-alerts"
                    class="chart-canvas"
                    style="height: 338px"
                  >
                    <div class="d-flex justify-content-center align-items-center h-100">
                      <div class="justify-content-between text-center">
                        <i class="fa fa-info-circle fa-2x"/>
                        <p class="text-muted">
                          No data available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div
              v-if="isLoadingBarChart"
              class="card z-index-2"
            >
              <div class="pb-0 pt-3 card-header mb-0">
                <h6>Most Alerts per Connector</h6>
              </div>
              <div class="p-3 card-body">
                <div class="chart">
                  <div
                    id="chart-bar-alerts"
                    class="chart-canvas"
                    style="height: 338px"
                  >
                    <div class="d-flex justify-content-center align-items-center h-100">
                      <div class="justify-content-between text-center">
                        <sh-loader
                          text="Loading..."
                          icon-size="fa-2x"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-else-if="!isLoadingBarChart && !barChartData"
              class="card z-index-2"
            >
              <div class="pb-0 pt-3 card-header mb-0">
                <h6>Most Alerts per Connector</h6>
              </div>
              <div class="p-3 card-body">
                <div class="chart">
                  <div
                    id="chart-bar-alerts"
                    class="chart-canvas"
                    style="height: 338px"
                  >
                    <div class="d-flex justify-content-center align-items-center h-100">
                      <div class="justify-content-between text-center">
                        <i class="fa fa-info-circle fa-2x"/>
                        <p class="text-muted">
                          No data available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <bar-chart
              v-if="barChartData && !isLoadingBarChart"
              id="chart-bar-alerts"
              title="Most Alerts per Connector"
              height="338"
              :chart="{
                labels: barChartData.labels,
                datasets: {
                  label: 'Open Alerts for this Connector',
                  data: barChartData.datasets.data,
                },
              }"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <sh-table
    title="Critical Alerts"
    :object-list="criticalAlerts"
    :page-data="criticalAlertsPageData"
    :headers="CRITICAL_ALERTS_HEADERS"
    :is-loading="isLoadingCriticalAlertsList"
    :is-row-clickable="true"
    :current-sorting="criticalAlertsOrdering"
    loading-text="Loading critical alerts..."
    empty-text="No critical alerts found."
    @change-page="loadCriticalAlerts"
    @click-row="handleRowClick"
    @sort="(field) => toggleSort(criticalAlertsOrdering, field, loadCriticalAlerts)"
  >
    <template #rows="{object}">
      <td class="ps-4">
        <div class="d-flex align-items-center">
          <h6 class="mb-0 text-sm alert-col me-2">
            {{ object.message }}
          </h6>
          <sh-badge
            v-if="object.recreated_count"
            :color="object.recreated_count ? 'danger' : 'secondary'"
            :label="object.recreated_count + 1"
          />
        </div>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.rule.description }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.rule.connector.name }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ stringToPrettyDateTime(object.created_at) }}
        </h6>
      </td>
      <td class="ps-4">
        <sh-badge
          :label="object.status"
          :color="STATUS_BADGE_CLASSES[object.status]"
          :full-width="true"
        />
      </td>
      <td class="ps-4">
        <sh-badge
          :label="object.severity"
          :color="SEVERITY_BADGE_CLASSES[object.severity]"
          :full-width="true"
        />
      </td>
    </template>
  </sh-table>
</template>

<style scoped>
.alert-col {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
