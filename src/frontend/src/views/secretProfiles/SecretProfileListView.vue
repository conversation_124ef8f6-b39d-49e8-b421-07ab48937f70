<script setup>
import {
  onMounted,
  ref,
} from 'vue';
import { fetchSecretProfiles } from '@/api/secretProfiles';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import { stringToPrettyDateTime } from '@/utils/datetime';
import { toggleSort } from '@/services/lists';
import ShInput from '@/components/generic/ShInput.vue';
import ShButton from '@/components/generic/ShButton.vue';
import ShBadge from "@/components/generic/ShBadge.vue";
import ShTable from '@/components/generic/ShTable.vue';

const store = useStore();

const secretProfiles = ref([]);
const pageData = ref({});
const isLoading = ref(true);
const search = ref('');
const ordering = ref('');

store.commit('setBreadcrumb', {
  currentDirectory: 'Secrets',
});

const HEADERS = [
  { label: 'Location', sortField: 'location' },
  { label: 'Key', sortField: 'key' },
  { label: 'Connector name', sortField: 'connector__name' },
  { label: 'Application', sortField: 'connector__application' },
  { label: 'Created at', sortField: 'created_at' },
  { label: 'Updated at', sortField: 'updated_at' },
  { label: 'Type' },
];

const _fetchSecrets = async (pageNumber) => {
  const response = await fetchSecretProfiles({
    search: search.value,
    ordering: ordering.value,
    pageNumber,
  });
  if (response.status !== 200) {
    console.error('Error fetching secrets:', response);
    isLoading.value = false;
    return;
  }
  secretProfiles.value = response.data.results;
  pageData.value = response.data.page;
  isLoading.value = false;
};



onMounted(async () => {
  await _fetchSecrets();
});
</script>

<template>
  <page-navbar
    title="Secrets"
    icon="key"
  />
  <sh-table
    :object-list="secretProfiles"
    :page-data="pageData"
    :headers="HEADERS"
    :is-loading="isLoading"
    :sortable="true"
    :current-sorting="ordering"
    loading-text="Loading secrets..."
    empty-text="No secrets found."
    @change-page="_fetchSecrets"
    @sort="(field) => toggleSort(ordering, field, _fetchSecrets)"
  >
    <template #actions>
      <form
        class="d-flex justify-content-end"
        @submit.prevent="_fetchSecrets()"
      >
        <div class="me-2">
          <sh-input
            id="secret-search-input"
            v-model="search"
            placeholder="Search..."
            type="text"
            class="input-group-alternative"
          />
        </div>
        <div>
          <sh-button
            id="secret-search-submit"
            variant="gradient"
            color="secondary"
            type="submit"
          >
            <i class="fa fa-search"></i>
          </sh-button>
          <sh-button
            v-if="search"
            id="secret-search-clear"
            class="ms-2"
            variant="gradient"
            color="danger"
            type="button"
            @click="search = ''; _fetchSecrets()"
          >
            <i class="fa fa-times"></i>
          </sh-button>
        </div>
      </form>
    </template>
    <template #rows="{object}">
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.location }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.key }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.connector.name }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.connector.application }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ stringToPrettyDateTime(object.created_at) }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ stringToPrettyDateTime(object.updated_at) }}
        </h6>
      </td>
      <td class="ps-4">
        <h6
          v-if="object.type"
          class="mb-0 text-sm"
        >
          <sh-badge
            color="info"
            :label="object.type.replace('_', ' ')"
          />
        </h6>
      </td>
    </template>
  </sh-table>
</template>
