<script setup>
import {
  fetchConnector,
  fetchConnectorStatistics,
  deleteConnector,
  startConnectorSync,
} from '@/api/connectors';
import { fetchSyncRuns } from '@/api/runs';
import ShAlert from '@/components/generic/ShAlert.vue';
import ShButton from '@/components/generic/ShButton.vue';
import {
  onMounted,
  ref,
} from 'vue';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import ShTable from '@/components/generic/ShTable.vue';
import { stringToPrettyDateTime } from '@/utils/datetime';
import { STATUS_BADGE_CLASSES } from '@/utils/constants';
import ShBadge from '@/components/generic/ShBadge.vue';
import {
  useRoute,
  useRouter,
} from 'vue-router';

const { commit } = useStore();
const router = useRouter();
const { params } = useRoute();

const connector = ref(null);
const stats = ref(null);
const syncRuns = ref(null);
const showDeleteDialog = ref(false);
const showDeleteConfirm = ref(false);
const confirmMessage = ref('');
const showSyncDialog = ref(false);
const showSyncConfirm = ref(false);

const HEADERS = [
  {
    label: 'Started',
  },
  {
    label: 'Ended',
  },
  {
    label: 'Status',
  },
]

const setBreadcrumb = () => {
  commit('setBreadcrumb', {
    currentDirectory: 'Connectors',
    detailPage: connector.value.name,
  });
};

const _fetchConnector = async () => {
  const response = await fetchConnector(params.id);
  if (response.status === 200) {
    connector.value = response.data;
    setBreadcrumb()
  } else {
    alert('Something went wrong.');
  }
};

const _fetchConnectorStatistics = async () => {
  const response = await fetchConnectorStatistics(params.id);
  if (response.status === 200) {
    stats.value = response.data;
  } else {
    alert('Something went wrong. Fetching connector failed.');
  }
};

const _deleteConnector = async () => {
  const response = await deleteConnector(params.id);
  if (response.status === 204) {
    showDeleteDialog.value = false;
    showDeleteConfirm.value = true;
    confirmMessage.value = `Connector ${connector.value.name} has been deleted successfully.`;
    await router.push({ name: 'connector-list' });
  } else {
    alert('Failed to delete connector. Please try again.');
  }
};

const _fetchSynchRuns = async () => {
  const response = await fetchSyncRuns({connectorId: params.id});
  if (response.status === 200) {
    syncRuns.value = response.data.results;
  } else {
    alert('Something went wrong. Fetching synchronizations failed.');
  }
};

const _startConnectorSync = async () => {
  const response = await startConnectorSync(connector.value.id);
  if (response.status === 200) {
    showSyncDialog.value = false;
    showSyncConfirm.value = true;
    await _fetchConnector();
    await _fetchConnectorStatistics();
  } else {
    alert('Failed to start synchronization. Please try again.');
  }
}

onMounted(() => {
  _fetchConnector();
  _fetchConnectorStatistics();
  _fetchSynchRuns();
});
</script>


<template>
  <page-navbar
    v-if="connector"
    :title="connector.name"
    icon="plug"
    :back-route="{ name: 'connector-list' }"
  >
    <template #actions>
      <sh-button
        id="connector-detail-run-sync"
        variant="gradient"
        color="info"
        size="sm"
        class="me-2 mb-0"
        title="Start Synchronization"
        @click="showSyncDialog = true"
      >
        <i class="fa fa-play"/>
      </sh-button>
      <router-link
        id="connector-detail-edit"
        class="btn btn-secondary btn-sm me-2 mb-0"
        title="Edit Connector"
        :to="{ name: 'connector-update', params: { id: connector.id } }"
      >
        <i class="fa fa-edit"/>
      </router-link>
      <sh-button
        id="connector-detail-delete"
        variant="gradient"
        color="danger"
        size="sm"
        class="mb-0"
        title="Delete Connector"
        @click="showDeleteDialog = true"
      >
        <i class="fa fa-trash-o"/>
      </sh-button>
    </template>
  </page-navbar>
  <div v-if="connector">
    <div class="mt-3 row">
      <div class="col-md-6 mb-3">
        <div class="card">
          <div class="card-header">
            <span>Details</span>
          </div>
          <div class="card-body pt-0">
            <table class="table mb-0">
              <tbody>
              <tr>
                <td>
                  <strong>Name</strong>
                </td>
                <td
                  class="text-nowrap overflow-hidden text-truncate"
                  style="max-width: 200px;"
                >
                  {{ connector.name }}
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Application</strong>
                </td>
                <td
                  class="text-nowrap overflow-hidden text-truncate"
                  style="max-width: 200px;"
                >
                  {{ connector.application }}
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Base URL</strong>
                </td>
                <td
                  class="text-nowrap overflow-hidden text-truncate"
                  style="max-width: 200px;"
                >
                  {{ connector.base_url }}
                </td>
              </tr>
              <tr v-if="connector.account">
                <td>
                  <strong>Account</strong>
                </td>
                <td
                  class="text-nowrap overflow-hidden text-truncate"
                  style="max-width: 200px;"
                >
                  {{ connector.account }}
                </td>
              </tr>
              <tr v-if="connector.username">
                <td>
                  <strong>Username</strong>
                </td>
                <td
                  class="text-nowrap overflow-hidden text-truncate"
                  style="max-width: 200px;"
                >
                  {{ connector.username }}
                </td>
              </tr>
              <tr>
                <td>
                  <strong>API Key / Token</strong>
                </td>
                <td>
                  <div>
                    *************************
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="card h-100">
          <div class="card-header">
            Statistics
          </div>
          <div
            v-if="stats"
            class="card-body d-flex justify-content-around align-items-center"
          >
            <div class="text-center">
              <h1 class="mb-0">
                {{ stats.secret_profiles }}
              </h1>
              <span class="text-muted">secrets</span>
            </div>
            <div class="text-center">
              <h1 class="mb-0">
                {{ stats.rule_runs }}
              </h1>
              <span class="text-muted">rule runs</span>
            </div>
            <div class="text-center">
              <h1 class="mb-0">
                {{ stats.sync_runs }}
              </h1>
              <span class="text-muted">synchronizations</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sh-table
      title="Synchronizations"
      :headers="HEADERS"
      :object-list="syncRuns"
    >
      <template #rows="{object}">
        <td>
          {{ stringToPrettyDateTime(object.started) }}
        </td>
        <td>
          {{ stringToPrettyDateTime(object.ended) }}
        </td>
        <td>
          <sh-badge
            :color="STATUS_BADGE_CLASSES[object.status]"
            :label="object.status"
          />
        </td>
      </template>
    </sh-table>
  </div>

  <sh-alert
    v-if="showDeleteDialog"
    title="Are you sure?"
    text="You won't be able to revert this!"
    icon="warning"
    confirm-button-text="Yes, delete it!"
    cancel-button-text="Cancel"
    :show-cancel-button="true"
    @confirmed="_deleteConnector"
    @cancel="showDeleteDialog = false"
  />
  <sh-alert
    v-if="showDeleteConfirm"
    title="Success"
    :text="confirmMessage"
    icon="success"
    confirm-button-text="OK"
  />
  <sh-alert
    v-if="showSyncDialog"
    title="Are you sure you want to start the synchronization process?"
    icon="question"
    confirm-button-text="Yes!"
    :show-cancel-button="true"
    cancel-button-text="Cancel"
    @confirmed="_startConnectorSync"
    @dismissed="showSyncDialog = false"
  />
  <sh-alert
    v-if="showSyncConfirm"
    title="Success"
    text="Synchronization has been started successfully."
    icon="success"
    confirm-button-text="OK"
    @confirmed="showSyncConfirm = false"
  />
</template>
