<script setup>
import { createConnector, testConnectorConnection } from '@/api/connectors';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { APPLICATIONS } from "@/utils/constants"
import ConnectorFormAnsible from '@/components/forms/ConnectorFormAnsible.vue';
import ConnectorFormConjur from '@/components/forms/ConnectorFormConjur.vue';
import ConnectorFormGithub from '@/components/forms/ConnectorFormGithub.vue';
import ConnectorFormGitlab from '@/components/forms/ConnectorFormGitlab.vue';
import ConnectorFormHashiCorpVault from '@/components/forms/ConnectorFormHashiCorpVault.vue';
import PageNavbar from '@/components/navigation/PageNavbar.vue';

const store = useStore();
const application = ref('');
const errors = ref({});
const router = useRouter();
const testConnectionResult = ref(null);
const isLoading = ref(false);


store.commit('setBreadcrumb', {
  currentDirectory: 'Connectors',
});

watch(application, () => {
  testConnectionResult.value = null;
});

const create = async (data) => {
  try {
    const response = await createConnector(data);
    if (response.status === 201) {
      await router.push({ name: 'connector-list' });
    } else {
      errors.value = response.data;
      console.log(response.data);
    }
  } catch (error) {
    console.log(error);
    const errorData = error.response.data;
    Object.keys(errorData).forEach((field) => {
      errors.value[field] = errorData[field][0];
    });
  }
};

const testConnection = async (data) => {
  isLoading.value = true;
  testConnectionResult.value = null;

  try {
    const response = await testConnectorConnection(data);
    testConnectionResult.value = {
      success: true,
      message: response.data.message
    };
  } catch (error) {
    const errorMessage = error.response.data.message;
    testConnectionResult.value = {
      success: false,
      message: errorMessage
    };
  } finally {
    isLoading.value = null;
  }
};

const getBackgroundImage = (application) => {
  const img = require(`@/assets/images/applications/${application}.png`);
  return `url('${img}')`;
}
</script>

<template>
  <page-navbar
    title="Add connector"
    icon="plus"
    :back-route="{ name: 'connector-list' }"
  />
  <h3 class="text-white">Choose an application:</h3>
  <div class="row">
    <div class="col-md-6">

      <div class="row">
        <div
          v-for="app in APPLICATIONS"
          :key="app.value"
          class="col-12 col-sm-6 col-xl-4 clickable"
          @click="application = app"
        >
          <div
            class="card card-body card-application mb-3"
            :class="{ 'card-application-selected': application.value === app.value }"
            :style="{'background-image': getBackgroundImage(app.value)}"
          />
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <transition name="fade-up">
        <div v-if="application" class="card card-body">
          <h3 class="text-center text-muted">
            {{ application.name }}
          </h3>
          <connector-form-conjur
            v-if="application.value === 'conjur'"
            :errors="errors"
            :test-connection-result="testConnectionResult"
            :is-testing-connection="isLoading"
            @submitted="create"
            @test-connection="testConnection"
            @cancel="() => router.push({ name: 'connector-list' })"
          />
          <connector-form-gitlab
            v-else-if="application.value === 'gitlab'"
            :errors="errors"
            :test-connection-result="testConnectionResult"
            :is-testing-connection="isLoading"
            @submitted="create"
            @test-connection="testConnection"
            @cancel="() => router.push({ name: 'connector-list' })"
          />
          <connector-form-hashi-corp-vault
            v-else-if="application.value === 'vault'"
            :errors="errors"
            :test-connection-result="testConnectionResult"
            :is-testing-connection="isLoading"
            @submitted="create"
            @test-connection="testConnection"
            @cancel="() => router.push({ name: 'connector-list' })"
          />
          <connector-form-ansible
            v-else-if="application.value === 'ansible'"
            :errors="errors"
            :test-connection-result="testConnectionResult"
            :is-testing-connection="isLoading"
            @submitted="create"
            @test-connection="testConnection"
            @cancel="() => router.push({ name: 'connector-list' })"
          />
          <connector-form-github
            v-else-if="application.value === 'github'"
            :errors="errors"
            :test-connection-result="testConnectionResult"
            :is-testing-connection="isLoading"
            @submitted="create"
            @test-connection="testConnection"
            @cancel="() => router.push({ name: 'connector-list' })"
          />
          <div v-else class="alert alert-danger">
            No form configured for this application.
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.card-application {
  height: 200px;
  background-size: contain;
  background-repeat: no-repeat; /* Prevents tiling of smaller images */
  background-position: center;
  transition: all 0.2s ease-in-out;
}

.card-application:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background-color: #e9ecef !important;
}

.card-application-selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background-color: #e9ecef !important;
}
</style>