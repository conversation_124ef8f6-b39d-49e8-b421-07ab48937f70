<script setup>
import ConnectorCard from '@/components/visualizations/ConnectorCard.vue';
import { fetchConnectors } from '@/api/connectors';
import {
  ref,
  onMounted,
} from 'vue';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import Sh<PERSON>oader from "@/components/generic/ShLoader.vue";
import ShButton from "@/components/generic/ShButton.vue";

const store = useStore();
const connectors = ref([]);
const loading = ref(true);

store.commit('setBreadcrumb', {
  currentDirectory: 'Connectors',
});

const fetchData = async () => {
  const response = await fetchConnectors();
  if (response.status === 200) {
    connectors.value = response.data;
    loading.value = false;
  } else {
    alert('Something went wrong.')
  }
};

onMounted(() => {
  fetchData();
});
</script>


<template>
  <page-navbar
    title="Connectors"
    icon="plug"
  >
    <template #actions>
      <sh-button
        id="connector-create-button"
        variant="gradient"
        color="info"
        size="sm"
        class="mb-0"
        :to="{ name: 'connector-create' }"
      >
        <i class="fa fa-plus"></i>
        <span class="ms-1">Add Connector</span>
      </sh-button>
    </template>
  </page-navbar>
  <div
    v-if="loading"
    class="card mb-4"
  >
    <div class="card-body">
      <sh-loader
        text="Loading connectors..."
      />
    </div>
  </div>
  <div
    v-else-if="connectors.length === 0"
    class="card mb-4">
    <div class="card-body">
      <p class="text-center my-2">
        No connectors found.
      </p>
    </div>
  </div>
  <div
    v-else-if="connectors.length > 0"
  >
    <div class="row">
      <div
        v-for="(connector, index) in connectors"
        :key="index"
        class="mb-3 col-12 col-md-6 col-xl-4"
      >
        <connector-card
          :id="connector.name"
          :username="connector.username"
          :account="connector.account"
          :url="connector.base_url"
          :application="connector.application"
          :name="connector.name"
          :active="connector.is_active"
          :last-sync-status="connector.last_sync_status"
          @click="$router.push({ name: 'connector-detail', params: { id: connector.id } })"
        />
      </div>
    </div>
  </div>
</template>
