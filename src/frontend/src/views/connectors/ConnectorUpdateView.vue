<script setup>
import { onMounted } from 'vue';
import ConnectorFormHashiCorpVault from '../../components/forms/ConnectorFormHashiCorpVault.vue';
import ConnectorFormConjur from '../../components/forms/ConnectorFormConjur.vue';
import ConnectorFormGitlab from '../../components/forms/ConnectorFormGitlab.vue';
import {
  fetchConnector,
  updateConnector,
} from '@/api/connectors';
import ConnectorFormAnsible from '@/components/forms/ConnectorFormAnsible.vue';
import ConnectorFormGithub from '@/components/forms/ConnectorFormGithub.vue';

import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';

const store = useStore();
const initialData = ref(null);
const errors = ref({});
const router = useRouter();


const setBreadcrumb = () => {
  store.commit('setBreadcrumb', {
    currentDirectory: 'Connectors',
    detailPage: initialData.value.name,
  });
};

const goToConnectorDetail = () => {
  router.push({
    name: 'connector-detail',
    params: { slug: router.currentRoute.value.params.id },
  });
};

const fetchConnectorDetails = async () => {
  const response = await fetchConnector(
    router.currentRoute.value.params.id,
  );
  if (response.status === 200) {
    initialData.value = response.data;
    setBreadcrumb()
  } else {
    alert(`Failed to fetch connector details for slug: ${router.currentRoute.value.params.id}.`);
  }
};

const updateConnectorDetails = async (formData) => {
  errors.value = {};
  console.log(router.currentRoute.value.params.id)
  let response;
  try {
    response = await updateConnector(
      router.currentRoute.value.params.id,
      formData,
    );
  } catch (error) {
    console.log(error)
    error.response.data.detail.forEach((err) => {
      errors.value[err.loc[1]] = err.msg;
    });
    return;
  }
  if (response.status === 200) {
    goToConnectorDetail();
  } else {
    alert(`Failed to update connector details for slug: ${router.currentRoute.value.params.id}.`);
  }
};

onMounted(() => {
  fetchConnectorDetails();
});
</script>

<template>
  <page-navbar
    v-if="initialData"
    :title="`Update ${initialData.name}`"
    icon="plug"
    :back-route="{ name: 'connector-detail', params: { slug: router.currentRoute.value.params.id } }"
  />
  <div class="card">
    <div class="card-body">
      <div>
        <label for="applicationSelect" class="form-label w-100">
            <span>
              <strong>Secrets Management Application</strong>
            </span>
          <select
            v-if="initialData"
            id="connector-form-application-select"
            v-model="initialData.application"
            class="form-select"
            aria-label="application_type"
            disabled
          >
            <option value="" disabled>-</option>
            <option value="conjur">Conjur</option>
            <option value="gitlab">GitLab</option>
            <option value="vault">HashiCorp Vault</option>
            <option value="ansible">Ansible</option>
          </select>
        </label>
      </div>
      <div v-if="initialData">
        <connector-form-hashi-corp-vault
          v-if="initialData.application === 'vault'"
          :initial-data="initialData"
          button-text-submit="Update Connector"
          :errors="errors"
          @submitted="updateConnectorDetails"
          @cancel="goToConnectorDetail"

        />
        <connector-form-conjur
          v-else-if="initialData.application === 'conjur'"
          :initial-data="initialData"
          button-text-submit="Update Connector"
          :errors="errors"
          @submitted="updateConnectorDetails"
          @cancel="goToConnectorDetail"
        />
        <connector-form-gitlab
          v-else-if="initialData.application === 'gitlab'"
          :initial-data="initialData"
          button-text-submit="Update Connector"
          :errors="errors"
          @submitted="updateConnectorDetails"
          @cancel="goToConnectorDetail"
        />
        <connector-form-ansible
          v-else-if="initialData.application === 'ansible'"
          :initial-data="initialData"
          button-text-submit="Update Connector"
          :errors="errors"
          @submitted="updateConnectorDetails"
          @cancel="goToConnectorDetail"
        />
        <connector-form-github
          v-else-if="initialData.application === 'github'"
          :initial-data="initialData"
          button-text-submit="Update Connector"
          :errors="errors"
          @submitted="updateConnectorDetails"
          @cancel="goToConnectorDetail"
        />
      </div>
    </div>
  </div>
</template>
