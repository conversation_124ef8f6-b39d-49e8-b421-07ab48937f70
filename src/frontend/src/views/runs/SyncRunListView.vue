<script setup>
import { useStore } from 'vuex';
import {
  onMounted,
  ref,
} from 'vue';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import ShBadge from '@/components/generic/ShBadge.vue';
import ShButton from "@/components/generic/ShButton.vue";
import ShInput from '@/components/generic/ShInput.vue';
import ShTable from '@/components/generic/ShTable.vue';
import { STATUS_BADGE_CLASSES } from '@/utils/constants';
import { stringToPrettyDateTime } from '@/utils/datetime';
import { fetchSyncRuns } from '@/api/runs';


const { commit } = useStore();
const syncRuns = ref([]);
const pageData = ref({});
const search = ref('');
const isLoading = ref(true);

commit('setBreadcrumb', {
  currentDirectory: 'Synchronizations',
});

const HEADERS = [
  {
    label: 'Connector',
  },
  {
    label: 'Created',
  },
  {
    label: 'Started',
  },
  {
    label: 'Ended',
  },
  {
    label: 'Status',
  },
]

const _fetchSynchRuns = async (pageNumber) => {
  const response = await fetchSyncRuns({search: search.value, pageNumber});
  if (response.status === 200) {
    syncRuns.value = response.data.results;
    pageData.value = response.data.page;
    isLoading.value = false;
  } else {
    alert('Something went wrong. Fetching synchronizations failed.');
  }
};

onMounted(async () => {
  await _fetchSynchRuns(1);
});
</script>

<template>
  <page-navbar
    title="Synchronizations"
    icon="sync"
  />
  <sh-table
    :object-list="syncRuns"
    :page-data="pageData"
    :headers="HEADERS"
    :is-loading="isLoading"
    loading-text="Loading synchronizations..."
    empty-text="No synchronizations found."
    @change-page="_fetchSynchRuns"
  >
    <template #actions>
      <form
        class="d-flex justify-content-end"
        @submit.prevent="() => _fetchSynchRuns(1)"
      >
        <div class="me-2">
          <sh-input
            id="sync-run-search-input"
            v-model="search"
            placeholder="Search..."
            type="text"
            class="input-group-alternative"
          />
          </div>
        <div>
          <sh-button
            id="sync-run-search-submit"
            variant="gradient"
            color="secondary"
            type="submit"
          >
            <i class="fa fa-search"></i>
          </sh-button>
        </div>
        <div v-if="search">
          <sh-button
            id="sync-run-clear-search"
            class="ms-2"
            variant="gradient"
            color="danger"
            type="button"
            @click="search = ''; _fetchSynchRuns(1)"
          >
            <i class="fa fa-times"></i>
          </sh-button>
        </div>
      </form>
    </template>
    <template #rows="{object}">
      <td>
        <span class="me-2">{{ object.connector.name }}</span>
        <router-link :to="{name: 'connector-detail', params: {id: object.connector.id}}">
          <i class="fa fa-external-link"></i>
        </router-link>
      </td>
      <td>{{ stringToPrettyDateTime(object.created) }}</td>
      <td>{{ stringToPrettyDateTime(object.started) }}</td>
      <td>{{ stringToPrettyDateTime(object.ended) }}</td>
      <td>
        <sh-badge
          :color="STATUS_BADGE_CLASSES[object.status]"
          :label="object.status"
        />
      </td>
    </template>
  </sh-table>
</template>
