<script setup>
import { useStore } from 'vuex';
import { watch } from 'vue';
import {
  updateUser,
} from '@/api/users';
import ShAlert from '@/components/generic/ShAlert.vue';
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import ShInput from '@/components/generic/ShInput.vue';
import ShButton from '@/components/generic/ShButton.vue';
import PageNavbar from '@/components/navigation/PageNavbar.vue';

const store = useStore();
const errors = ref({});
const router = useRouter();
const password = ref(null);
const passwordConfirm = ref(null);
const showPasswordEditedDialog = ref(false);
const showInformationEditedDialog = ref(false);
const formData = ref({
  first_name: null,
  last_name: null,
  email: null,
  phone: null,
});


const initialiseForm = async () => {
  formData.value.first_name = store.state.user.first_name
  formData.value.last_name = store.state.user.last_name
  formData.value.email = store.state.user.email
  formData.value.phone = store.state.user.phone
};

const setBreadcrumb = () => {
  store.commit('setBreadcrumb', {
    currentDirectory: 'Profile',
    detailPage: store.state.user.username,
  });
};

const verifyPassword = () => {
  if (password.value !== passwordConfirm.value) {
    errors.value.password = 'Passwords do not match';
  } else {
    delete errors.value.password;
  }
};

const updateUserPassword = async (formData) => {
  verifyPassword();
  if (errors.value.password) {
    return;
  }
  let response;
  try {
    response = await updateUser(
      store.state.user.id,
      formData,
    );
  } catch (error) {
    console.log(error)
    error.response.data.detail.forEach((err) => {
      errors.value[err.loc[1]] = err.msg;
    });
  }
  if (response.status === 200) {
    showPasswordEditedDialog.value = true;
    await router.push({ name: 'user-profile', query: { updated: true } });
  } else {
    alert('Something went wrong. Updating a user failed.');
  }
};

const updateUserDetails = async (formData) => {
  let response;
  try {
    response = await updateUser(
      store.state.user.id,
      formData,
    );
  } catch (error) {
    console.log(error)
    error.response.data.detail.forEach((err) => {
      errors.value[err.loc[1]] = err.msg;
    });
  }
  if (response.status === 200) {
    showInformationEditedDialog.value = true;
  } else {
    alert('Something went wrong. Updating a user failed.');
  }
};

watch(
  () => store.state.user,
  (user) => {
    if (user && user.id) {
      initialiseForm();
      setBreadcrumb();
    }
  },
  { immediate: true },
);


</script>
<template>
  <page-navbar
    :title="store.state.user.username"
    icon="user"
  />
  <div class="card mb-3">
    <div class="card-header">
      <h5>Basic Info</h5>
    </div>
    <form
      id="user-profile-form"
      @submit.prevent
    >
      <div class="card-body pt-0">
        <div class="row">
          <div class="col-6">
            <label class="form-label">First Name</label>
            <sh-input
              id="firstname"
              v-model="formData.first_name"
              type="text"
            />
          </div>
          <div class="col-6">
            <label class="form-label">Last Name</label>
            <sh-input
              id="lastname"
              v-model="formData.last_name"
              type="text"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <label class="form-label mt-2">Email</label>
            <sh-input
              id="email"
              v-model="formData.email"
              class="mb-0"
              type="email"
            />
          </div>
          <div class="col-6">
            <label class="form-label mt-2">Phone Number</label>
            <sh-input
              id="phone"
              v-model="formData.phone"
              class="mb-0"
              type="text"
            />
          </div>
        </div>
      </div>
      <sh-button
        class="float-end mb-4 mx-4"
        color="info"
        variant="gradient"
        size="sm"
        @click="updateUserDetails(formData)"
      >
        Update profile
      </sh-button>
    </form>
  </div>
  <div id="password" class="card">
    <div class="card-header">
      <h5>Change Password</h5>
    </div>
    <form
      id="user-profile-form"
      @submit.prevent
      @keydown="errors = {}"
    >
      <div class="card-body pt-0">
        <label class="form-label">New password</label>
        <sh-input
          id="new-password"
          v-model="password"
          type="password"
          placeholder="New Password"
        />
        <label class="form-label">Confirm new password</label>
        <sh-input
          id="confirm-password"
          v-model="passwordConfirm"
          class="mb-0"
          type="password"
          placeholder="Confirm password"
        />
        <p v-if="errors.password" class="text-danger">
          {{ errors.password }}
        </p>
      </div>
      <sh-button
        class="float-end mb-4 mx-4"
        color="info"
        variant="gradient"
        size="sm"
        @click="updateUserPassword({ password: password, passwordConfirm: passwordConfirm })"
      >
        Update password
      </sh-button>
    </form>
  </div>
  <sh-alert
    v-if="showPasswordEditedDialog"
    :show="showPasswordEditedDialog"
    title="Password updated"
    text="User password was successfully updated."
    icon="success"
    confirm-button-text="OK"
    @confirm="showPasswordEditedDialog = false"
  />
  <sh-alert
    v-if="showInformationEditedDialog"
    :show="showInformationEditedDialog"
    title="User information updated"
    text="User information was successfully updated."
    icon="success"
    confirm-button-text="OK"
    @confirm="showInformationEditedDialog = false"
  />
</template>
