<script setup>
import {
  fetchRule,
  deleteRule,
  runRule,
} from '@/api/rules';
import { fetchRuleRuns } from '@/api/runs';
import {
  onMounted,
  ref,
} from 'vue';
import { useRouter } from 'vue-router';
import ShAlert from '../../components/generic/ShAlert.vue';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import ShButton from "@/components/generic/ShButton.vue";
import ShInput from "@/components/generic/ShInput.vue";
import ShBadge from "@/components/generic/ShBadge.vue";
import { stringToPrettyDateTime } from '@/utils/datetime';
import Paginator from "@/components/lists/Paginator.vue";
import {
  SEVERITY_BADGE_CLASSES,
  STATUS_BADGE_CLASSES,
  RULE_TYPES,
} from "@/utils/constants";

const store = useStore();
const rule = ref(null);
const ruleRuns = ref(null);
const router = useRouter();
const showDeleteDialog = ref(false);
const showDeleteConfirm = ref(false);
const showRunDialog = ref(false);
const showRunConfirm = ref(false);
const confirmMessage = ref('');
const search = ref('');


const setBreadcrumb = () => {
  store.commit('setBreadcrumb', {
    currentDirectory: 'Rules',
    detailPage: rule.value.description,
  });
};


const openDeleteDialog = () => {
  showDeleteDialog.value = true;
};


const deleteRuleDetails = async () => {
  const response = await deleteRule(
    router.currentRoute.value.params.id,
  );
  if (response.status === 204) {
    showDeleteDialog.value = false;
    showDeleteConfirm.value = true;
    confirmMessage.value = `Rule ${rule.value.description} has been deleted successfully.`;
    await router.push({ name: 'rule-list' });
  } else {
    alert('Failed to delete rule. Please try again.');
  }
};


const fetchRuleDetails = async () => {
  const response = await fetchRule(
    router.currentRoute.value.params.id,
  );
  if (response.status === 200) {
    rule.value = response.data;
    setBreadcrumb();
  } else {
    alert('Something went wrong. Fetching rule failed.');
  }
};


const _fetchRuleRuns = async (pageNumber) => {
  const response = await fetchRuleRuns({
    search: search.value,
    ruleId: router.currentRoute.value.params.id,
    pageNumber
  });
  if (response.status === 200) {
    ruleRuns.value = response.data;
  } else {
    alert('Something went wrong. Fetching rule runs failed.');
  }
};


const startRule = async () => {
  const response = await runRule(rule.value.id);
  if (response.status === 200) {
    showRunDialog.value = false;
    showRunConfirm.value = true;
    await fetchRuleDetails();
    await _fetchRuleRuns();
  } else {
    alert('Failed to start rule. Please try again.');
  }
}

onMounted(
  async () => {
    await fetchRuleDetails();
    await _fetchRuleRuns();
  },
)

</script>

<template>
  <page-navbar
    v-if="rule"
    :title="rule.description"
    icon="file"
    :back-route="{ name: 'rule-list' }"
  >
    <template #actions>
      <sh-button
        id="rule-detail-run-sync"
        variant="gradient"
        color="info"
        size="sm"
        class="me-2 mb-0"
        @click="showRunDialog = true"
      >
        <i class="fa fa-play"/>
      </sh-button>
      <router-link
        id="rule-detail-edit"
        class="btn btn-secondary btn-sm me-2 mb-0"
        :to="{ name: 'rule-update' }"
      >
        <i class="fa fa-edit"/>
      </router-link>
      <sh-button
        id="rule-detail-delete"
        variant="gradient"
        color="danger"
        size="sm"
        class="mb-0"
        @click="openDeleteDialog"
      >
        <i class="fa fa-trash-o"/>
      </sh-button>
    </template>
  </page-navbar>
  <div
    v-if="rule"
    class="row"
  >
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-header">
          <span>Details</span>
        </div>
        <div class="card-body pt-0">
          <table class="table mb-0">
            <tbody>
            <tr>
              <td>
                <strong>Connector</strong>
              </td>
              <td>
                <router-link :to="{ name: 'connector-detail', params: { id: rule.connector.id } }">
                  {{ rule.connector.name }}
                </router-link>
              </td>
            </tr>
            <tr>
              <td>
                <strong>Rule type</strong>
              </td>
              <td>{{ RULE_TYPES.find(type => type.id === rule.type)?.name }}</td>
            </tr>
            <tr>
              <td>
                <strong>Alert severity</strong>
              </td>
              <td>
                <sh-badge
                  variant="gradient"
                  :color="SEVERITY_BADGE_CLASSES[rule.alert_severity]"
                  :label="rule.alert_severity"
                />
              </td>
            </tr>
            <tr>
              <td>
                <strong>Frequency</strong>
              </td>
              <td>{{ rule.frequency_name }}</td>
            </tr>
            <tr>
              <td>
                <strong>Active</strong>
              </td>
              <td>
                <sh-badge
                  v-if="rule.is_active"
                  color="success"
                  variant="gradient"
                >
                  <i class="fa fa-check"/>
                </sh-badge>
                <sh-badge
                  v-else
                  color="danger"
                  variant="gradient"
                >
                  <i class="fa fa-times"/>
                </sh-badge>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="col-md-6 mb-3">
      <div class="card h-100">
        <div class="card-header">
          Open Alerts
        </div>
        <div class="card-body">
          <div
            class="card-body d-flex justify-content-around align-items-center"
          >
            <h1
              v-if="rule.alert_count > 0"
              class="text-danger"
            >
              {{ rule.alert_count }}
            </h1>
            <h1
              v-else
              class="text-secondary"
            >
              0
            </h1>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="ruleRuns && ruleRuns.page.count > 0"
    class="card mb-4"
  >
    <div class="card-header pb-0">
      <form
        class="d-flex justify-content-end"
        @submit.prevent="_fetchRuleRuns(page)"
      >
        <div class="me-2">
          <sh-input
            v-model="search"
            placeholder="Search..."
            type="text"
            class="input-group-alternative"
          />
        </div>
        <div>
          <sh-button
            variant="gradient"
            color="secondary"
            type="submit"
          >
            <i class="fa fa-search"></i>
          </sh-button>
        </div>
      </form>
    </div>
    <div class="card-body px-0 pt-0 pb-2">
      <div class="table-responsive p-0">
        <table class="table align-items-center justify-content-center mb-0">
          <thead>
          <tr>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder"
            >
              Started
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder"
            >
              Ended
            </th>
            <th
              class="ps-3 text-uppercase text-secondary text-xxs font-weight-bolder"
            >
              Status
            </th>
          </tr>
          </thead>
          <tbody>
            <tr
              v-for="(run) in ruleRuns.results"
              :key="run.id"
              >
              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  {{ stringToPrettyDateTime(run.started) }}
                </h6>
              </td>
              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  {{ stringToPrettyDateTime(run.ended) }}
                </h6>
              </td>
              <td class="ps-3">
                <h6 class="mb-0 text-sm">
                  <sh-badge
                    :color="STATUS_BADGE_CLASSES[run.status]"
                    :label="run.status"
                  />
                </h6>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      v-if="ruleRuns"
      class="card-footer d-flex justify-content-end mb-0"
    >
      <paginator
        :page-data="ruleRuns.page"
        @change="_fetchRuleRuns"
      />
    </div>
  </div>
  <sh-alert
    v-if="showDeleteDialog"
    title="Are you sure?"
    text="You won't be able to revert this!"
    icon="warning"
    :confirm-button-text="'Yes, delete it!'"
    :show-cancel-button="true"
    :cancel-button-text="'Cancel'"
    @confirmed="deleteRuleDetails"
    @cancel="showDeleteDialog = false"
  />
  <sh-alert
    v-if="showDeleteConfirm"
    title="Success"
    :text="confirmMessage"
    icon="success"
    confirm-button-text="OK"
  />
  <sh-alert
    v-if="showRunDialog"
    title="Are you sure you want to start the rule?"
    icon="question"
    confirm-button-text="Yes!"
    :show-cancel-button="true"
    cancel-button-text="Cancel"
    @confirmed="startRule"
    @dismissed="showRunDialog = false"
  />
  <sh-alert
    v-if="showRunConfirm"
    title="Success"
    text="Run has been started successfully."
    icon="success"
    confirm-button-text="OK"
    @confirmed="showRunConfirm = false"
  />
</template>
