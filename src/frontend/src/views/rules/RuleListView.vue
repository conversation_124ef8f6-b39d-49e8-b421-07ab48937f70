<script setup>
import {
  onMounted,
  ref,
} from 'vue';
import { fetchRules } from '@/api/rules';
import ShBadge from '@/components/generic/ShBadge.vue';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import { SEVERITY_BADGE_CLASSES, RULE_TYPES } from "@/utils/constants";
import ShButton from "@/components/generic/ShButton.vue";

const store = useStore();
const rules = ref(null);
const page = ref(1);

store.commit('setBreadcrumb', {
  currentDirectory: 'Rules',
});

const loadRules = async () => {
  const response = await fetchRules({
    page: page.value,
    limit: 25
  });
  if (response.status === 200) {
    rules.value = response.data;
  } else {
    alert('Something went wrong. Fetching rules failed.');
  }
};

onMounted(async () => {
  await loadRules(1);
});

</script>

<template>
  <page-navbar
    title="Rules"
    icon="file"
  >
    <template #actions>
      <sh-button
        id="rule-create-button"
        variant="gradient"
        color="info"
        size="sm"
        class="mb-0"
        :to="{ name: 'rule-create' }"
      >
        <i class="fa fa-plus"/>
        <span class="ms-1">Add Rule</span>
      </sh-button>
    </template>
  </page-navbar>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="table-responsive">
          <table class="table m-0">
            <thead class="thead-light">
            <tr>
              <th class="ps-3 py-3">Connector</th>
              <th class="py-3">Description</th>
              <th class="py-3">Rule type</th>
              <th class="py-3">Alert Severity</th>
              <th class="py-3">Frequency</th>
              <th class="py-3">Active</th>
              <th class="pe-3 py-3 text-center">Open Alerts</th>
            </tr>
            </thead>
            <tbody>
            <router-link
              v-for="(rule, index) in rules"
              v-slot="{ navigate }"
              :key="rule.id"
              :to="{ name: 'rule-detail', params: { id: rule.id } }"
              custom
            >
              <tr
                :id="`rule-list-row-${index}`"
                class="clickable py-3"
                role="button"
                @click="navigate"
              >
                <td class="ps-3 py-3">{{ rule.connector.name }}</td>
                <td class="py-3">{{ rule.description }}</td>
                <td class="py-3">{{ RULE_TYPES.find(type => type.id === rule.type)?.name }}</td>
                <td class="py-3">
                  <sh-badge
                    :color="SEVERITY_BADGE_CLASSES[rule.alert_severity]"
                    :label="rule.alert_severity"
                  />
                </td>
                <td class="py-3">
                  <span class="badge bg-secondary font-monospace">
                    {{ rule.frequency_cron }}
                  </span>
                  {{ rule.frequency_name }}
                </td>
                <td class="py-3">
                  <div v-if="rule.is_active">
                    <sh-badge
                      color="success"
                      icon="check"
                    />
                  </div>
                  <div v-else>
                    <sh-badge
                      color="danger"
                      icon="times"
                    />
                  </div>
                </td>
                <td
                  v-if="rule.alert_count > 0"
                  class="text-sm rule-col py-3"
                >
                  <div class="d-flex justify-content-center">
                    <span class="badge bg-warning text-white">
                      {{ rule.alert_count }}
                    </span>
                  </div>

                </td>
                <td
                  v-else
                  class="text-sm rule-col py-3"
                >
                  <div class="d-flex justify-content-center">
                    <span class="badge bg-secondary text-dark">
                      0
                    </span>
                  </div>
                </td>
              </tr>
            </router-link>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.clickable {
  cursor: pointer;
}

.clickable:hover {
  background-color: #f8f9fa;
}

th + th,
td + td {
  padding-left: 32px;
}
</style>