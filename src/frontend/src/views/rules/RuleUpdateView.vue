<script setup>
import RuleForm from '../../components/forms/RuleForm.vue';
import {
  updateRule,
  fetchRule,
} from '@/api/rules';
import {
  onMounted,
  ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';

const store = useStore();
const initialData = ref(null);
const errors = ref({});
const router = useRouter();


const setBreadcrumb = () => {
  store.commit('setBreadcrumb', {
    currentDirectory: 'Rules',
    detailPage: initialData.value.description,
  });
};

const update = async (formData) => {
  errors.value = {};
  let response;
  try {
    response = await updateRule(
      router.currentRoute.value.params.id,
      formData,
    );
  } catch (error) {
    console.log(error);
    error.response.data.detail.forEach((err) => {
      errors.value[err.loc[1]] = err.msg;
    });
    return;
  }
  if (response.status === 200) {
    await router.push({ name: 'rule-list', query: { updated: true } });
  } else {
    alert('Something went wrong. Updating a rule failed.');
  }
};

const fetch = async () => {
  const response = await fetchRule(
    router.currentRoute.value.params.id,
  );
  if (response.status === 200) {
    initialData.value = response.data;
    setBreadcrumb();
  } else {
    alert(`Failed to fetch rule details for id: ${router.currentRoute.value.params.id}.`);
  }
};

onMounted(() => {
  fetch();
});

</script>

<template>
  <page-navbar
    v-if="initialData"
    :title="`Update ${initialData.description}`"
    icon="file"
    :back-route="{ name: 'rule-detail', params: { id: router.currentRoute.value.params.id } }"
  />
  <div class="card">
    <div class="card-body">
      <rule-form
        v-if="initialData"
        :initial-data="initialData"
        button-text-submit="Update rule"
        :errors="errors"
        @submitted="update"
        @cancel="() => router.push({ name: 'rule-list' })"
      />
    </div>
  </div>
</template>