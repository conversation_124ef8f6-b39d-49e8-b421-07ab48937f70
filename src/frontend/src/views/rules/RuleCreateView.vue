<script setup>
import RuleForm from '../../components/forms/RuleForm.vue';
import { createRule } from '@/api/rules';
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { useStore } from 'vuex';
import PageNavbar from '@/components/navigation/PageNavbar.vue';

const store = useStore();
const router = useRouter();
const errors = ref({});

store.commit('setBreadcrumb', {
  currentDirectory: 'Rules',
});

const create = async (formData) => {
  errors.value = {};
  let response;
  try {
    response = await createRule(formData);
  } catch (error) {
    const errorData = error.response.data;
    Object.keys(errorData).forEach((field) => {
      errors.value[field] = errorData[field][0];
    });
    return;
  }
  if (response && response.status === 201) {
    router.push({ name: 'rule-list', query: { created: true } });
  } else {
    alert('Something went wrong. Creating a rule failed.');
  }
};
</script>

<template>
  <page-navbar
    title="Add rule"
    icon="plus"
    :back-route="{ name: 'rule-list' }"
  />
  <div class="card">
    <div class="card-body">
      <rule-form
        button-text-submit="Create rule"
        :errors="errors"
        @submitted="create"
        @cancel="() => router.push({ name: 'rule-list' })"
      />
    </div>
  </div>
</template>
