<script setup>
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import { ref, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import ShButton from "@/components/generic/ShButton.vue";
import ShTable from '@/components/generic/ShTable.vue';
import ShInput from '@/components/generic/ShInput.vue';
import ShBadge from '@/components/generic/ShBadge.vue';
import ShAlert from '@/components/generic/ShAlert.vue';
import { fetchAlerts, updateAlert } from '@/api/alerts';
import { fetchUsers } from '@/api/users';
import { stringToPrettyDateTime } from '@/utils/datetime';

import {
  SEVERITY_BADGE_CLASSES,
  STATUS_BADGE_CLASSES,
} from '@/utils/constants';

const store = useStore();
const router = useRouter();
const tab = ref('open');
const alerts = ref([]);
const pageData = ref({});
const search = ref('');
const isLoading = ref(true);
const statuses = ref(['initial']);
const users = ref([]);
const selectedUserId = ref(null);
const showConfirm = ref(false);
const confirmMessage = ref('');
const ordering = ref('');

store.commit('setBreadcrumb', {
  currentDirectory: 'Alerts',
});

const HEADERS = [
  { label: 'Message', sortField: 'message' },
  { label: 'Rule', sortField: 'rule' },
  { label: 'Connector', sortField: 'rule__connector' },
  { label: 'Date', sortField: 'created_at' },
  { label: 'Assignee', sortField: 'assignee__username' },
  { label: 'Status', sortField: 'status' },
  { label: 'Severity', sortField: 'severity' },
];

function showOpenAlerts() {
  statuses.value = ['initial'];
  tab.value = 'open';
}

function showClosedAlerts() {
  statuses.value = ['resolved', 'deleted'];
  tab.value = 'closed';
}

const _fetchAlerts = async (pageNumber) => {
  const response = await fetchAlerts({
    search: search.value,
    statuses: statuses.value,
    ordering: ordering.value,
    pageNumber,
  });
  if (response.status === 200) {
    alerts.value = response.data.results;
    pageData.value = response.data.page;
    isLoading.value = false;
  } else {
    alert('Something went wrong. Fetching alerts failed.');
    isLoading.value = false;
  }
};

const _fetchUsers = async () => {
  const response = await fetchUsers();
  if (response.status === 200) {
    users.value = response.data;
  } else {
    console.log('Error fetching users:', response);
  }
};

const _updateAlert = async (alertId, updateData) => {
  const response = await updateAlert(alertId, updateData);
  if (response.status === 200) {
    showConfirm.value = true;
    confirmMessage.value = `Alert ${alertId} has been updated successfully.`;
  } else {
    console.error('Error updating alert status:', response.data);
  }
};

const closeShowConfirm = () => {
  showConfirm.value = false;
};

const handleRowClick = (alert) => {
  router.push({ name: 'alert-detail', params: { id: alert.id } });
};

watch(() => statuses.value, () => _fetchAlerts(), { deep: true });
watch(showConfirm, (newValue) => {
  if (newValue) {
    _fetchAlerts()
  }
}, { immediate: true });

onMounted(async () => {
  await _fetchAlerts();
  await _fetchUsers();
});
</script>

<template>
  <page-navbar
    title="Alerts"
    icon="exclamation-triangle"
  />
  <sh-table
    :object-list="alerts"
    :page-data="pageData"
    :headers="HEADERS"
    :is-loading="isLoading"
    :is-row-clickable="true"
    :current-sorting="ordering"
    loading-text="Loading alerts..."
    empty-text="No alerts found."
    @change-page="_fetchAlerts"
    @click-row="handleRowClick"
    @sort="(field) => toggleSort(ordering, field, _fetchAlerts)"
  >
    <template #actions>
      <form
        class="d-flex justify-content-end"
        @submit.prevent="_fetchAlerts()"
      >
        <div class="position-relative p-0 me-2">
          <ul class="nav">
            <li class="nav-item">
              <sh-button
                id="open-alerts-tab"
                :variant="tab === 'open' ? 'gradient' : 'outline'"
                color="primary"
                size="md"
                :class="`nav-link ${tab === 'open' ? 'text-white active' : 'text-secondary'} me-2`"
                @click="showOpenAlerts"
              >
                Open
              </sh-button>
            </li>
            <li class="nav-item">
              <sh-button
                id="closed-alerts-tab"
                :variant="tab === 'closed' ? 'gradient' : 'outline'"
                color="primary"
                size="md"
                :class="`nav-link ${tab === 'closed' ? 'text-white active' : 'text-secondary'}`"
                @click="showClosedAlerts"
              >
                Closed
              </sh-button>
            </li>
          </ul>
        </div>
        <div class="me-2">
          <sh-input
            id="alert-search-input"
            v-model="search"
            placeholder="Search..."
            type="text"
            class="input-group-alternative"
          />
        </div>
        <div>
          <sh-button
            id="alert-search-submit"
            variant="gradient"
            color="secondary"
            type="submit"
          >
            <i class="fa fa-search"></i>
          </sh-button>
          <sh-button
            v-if="search"
            id="alert-clear-search"
            class="ms-2"
            variant="gradient"
            color="danger"
            type="button"
            @click="search = ''; _fetchAlerts()"
          >
            <i class="fa fa-times"></i>
          </sh-button>
        </div>
      </form>
    </template>
    <template #rows="{object}">
      <td class="ps-4">
        <div class="d-flex align-items-center">
          <h6 class="mb-0 text-sm alert-col me-2">
            {{ object.message }}
          </h6>
          <sh-badge
            v-if="object.recreated_count"
            :color="object.recreated_count ? 'danger' : 'secondary'"
            :label="object.recreated_count + 1"
            title="Recreated count"
          />
        </div>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.rule.description }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ object.rule.connector.name }}
        </h6>
      </td>
      <td class="ps-4">
        <h6 class="mb-0 text-sm">
          {{ stringToPrettyDateTime(object.created_at) }}
        </h6>
      </td>
      <td class="ps-4">
        <h6
          v-if="object.assignee"
          class="mb-0 text-sm"
        >
          {{ object.assignee.username }}
        </h6>
        <div v-else-if="statuses.includes('initial') && !object.assignee">
          <select
            id="alert-assignee-select"
            v-model="selectedUserId"
            class="form-select-sm"
            @click.stop
            @change="_updateAlert(object.id, { assignee: selectedUserId }); selectedUserId = null"
          >
            <option selected disabled>-</option>
            <option
              v-for="user in users"
              :key="user.id"
              :value="user.id"
            >
              {{ user.username }}
            </option>
          </select>
        </div>
      </td>
      <td class="ps-3">
        <sh-badge
          :label="object.status"
          :color="STATUS_BADGE_CLASSES[object.status]"
          :full-width="true"
        />
      </td>
      <td class="ps-3">
        <sh-badge
          :label="object.severity"
          :color="SEVERITY_BADGE_CLASSES[object.severity]"
          :full-width="true"
        />
      </td>
    </template>
  </sh-table>
  <sh-alert
    v-if="showConfirm"
    title="Success"
    :text="confirmMessage"
    icon="success"
    confirm-button-text="OK"
    @confirmed="closeShowConfirm"
  />
</template>

<style scoped>
.alert-col {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>