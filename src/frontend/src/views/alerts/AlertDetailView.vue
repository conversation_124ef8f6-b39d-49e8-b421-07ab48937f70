<script setup>
import TimelineList from '@/components/lists/TimelineList.vue';
import TimelineItem from '@/components/visualizations/TimelineItem.vue';
import { stringToPrettyDateTime } from '@/utils/datetime';
import ShBadge from "@/components/generic/ShBadge.vue";
import PageNavbar from '@/components/navigation/PageNavbar.vue';
import ShAlert from "@/components/generic/ShAlert.vue";
import { fetchAlert, updateAlert } from '@/api/alerts';
import { fetchUsers } from '@/api/users';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { onMounted, ref } from 'vue';
import {
  SEVERITY_BADGE_CLASSES,
  STATUS_BADGE_CLASSES
} from "@/utils/constants";
import ShButton from "@/components/generic/ShButton.vue";


const STATUS_ICONS = {
  created: 'ni ni-bell-55',
  recreated: 'ni ni-bell-55',
  status_changed: 'ni ni-bell-55',
  assigned: 'ni ni-circle-08',
  commented: 'ni ni-chat-round'
}

const router = useRouter();
const store = useStore();
const alert = ref(null);
const users = ref([]);
const selectedUserId = ref(null);
const showDialog = ref(false);
const dialogText = ref("");
const confirmMessage = ref("");
const selectedAlertId = ref(null);
const selectedStatus = ref(null);
const showConfirm = ref(false);
const isEditingAssignee = ref(false);

const setBreadcrumb = () => {
  store.commit('setBreadcrumb', {
    currentDirectory: 'Alerts',
    detailPage: alert.value.message,
  });
};

const _fetchUsers = async () => {
  const response = await fetchUsers();
  if (response.status === 200) {
    users.value = response.data;
  } else {
    console.log("Error fetching users:", response);
  }
};

const _updateAlert = async (alertId, updateData) => {
  const response = await updateAlert(alertId, updateData);
  if (response.status === 200) {
    showDialog.value = false;
    showConfirm.value = true;
    confirmMessage.value = `Alert ${alertId} has been updated successfully.`;
    isEditingAssignee.value = false;
    await _fetchAlert();
  } else {
    console.error("Error updating alert status:", response.data);
  }
}

const _fetchAlert = async () => {
  const response = await fetchAlert(
    router.currentRoute.value.params.id,
  );
  if (response.status === 200) {
    alert.value = response.data;
    setBreadcrumb();
  } else {
    alert('Something went wrong.');
  }
};

const getColor = (event) => {
  if (event.event_type === 'status_changed' && event.metadata.to === 'resolved') {
    return 'success';
  }
  if (event.event_type === 'status_changed' && event.metadata.to === 'deleted') {
    return 'secondary';
  }
  if (event.event_type === 'created') {
    return 'danger';
  }
  if (event.event_type === 'recreated') {
    return 'danger';
  }
  if (event.event_type === 'assigned') {
    return 'primary';
  }
  if (event.event_type === 'commented') {
    return 'info';
  }
  return 'info';
};

const getTitle = (event) => {
  if (event.event_type === 'status_changed') {
    return `User ${event.metadata.user} changed status from ${event.metadata.from} to ${event.metadata.to}`;
  }
  if (event.event_type === 'assigned' && event.metadata.from !== null) {
    return `User ${event.metadata.user} assigned the alert from ${event.metadata.from} to ${event.metadata.to}`;
  }
  if (event.event_type === 'assigned' && event.metadata.from === null) {
    return `User ${event.metadata.user} assigned the alert to ${event.metadata.to}`;
  }
  if (event.event_type === 'commented') {
    return `User ${event.metadata.user} left a comment`;
  }
  if (event.event_type === 'created') {
    return `Alert created`;
  }
  if (event.event_type === 'recreated') {
    return `Alert recreated`;
  }
};

onMounted(
  () => {
    _fetchAlert();
    _fetchUsers();
  },
)

</script>
<template>
  <page-navbar
    v-if="alert"
    :title="alert.message"
    icon="exclamation-triangle"
    :back-route="{ name: 'alerts' }"
  >
    <template
      v-if="alert.status !== 'resolved' && alert.status !== 'deleted'"
      #actions
    >
      <sh-button
        id="alert-resolve"
        variant="gradient"
        color="success"
        size="sm"
        class="me-2 mb-0"
        title="Resolve Alert"
        :data-alert-id="alert.id"
        @click="showDialog = true; selectedAlertId = alert.id; selectedStatus = 'resolved'; dialogText = 'Resolve: ' + alert.message"
      >
        <i class="fa fa-check"/>
      </sh-button>
      <sh-button
        id="alert-delete"
        variant="gradient"
        color="danger"
        size="sm"
        class="mb-0"
        title="Delete Alert"
        :data-alert-id="alert.id"
        @click="showDialog = true; selectedAlertId = alert.id; selectedStatus = 'deleted'; dialogText = 'Delete: ' + alert.message"
      >
        <i class="fa fa-trash-o"/>
      </sh-button>
    </template>
  </page-navbar>
  <div class="container-fluid p-0">
    <div class="row">
      <div
        v-if="alert"
        class="col-lg-6"
      >
        <div class="card">
          <div class="card-header pb-0">
            <span>Alert Details</span>
            <div class="p-3 card-body">
              <table class="table">
                <tbody>
                  <tr>
                    <td>
                      <strong>Status</strong>
                    </td>
                    <td
                      class="gradient"
                    >
                      <sh-badge
                        variant="gradient"
                        :color="STATUS_BADGE_CLASSES[alert.status]"
                        :label="alert.status"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Rule</strong>
                    </td>
                    <td
                      class="text-nowrap overflow-hidden text-truncate"
                      style="max-width: 200px;"
                    >
                      {{ alert.rule.description }}
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Alert Message</strong>
                    </td>
                    <td
                      class="text-nowrap overflow-hidden text-truncate"
                      style="max-width: 200px;"
                    >
                      {{ alert.message }}
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Severity</strong>
                    </td>
                    <td>
                      <sh-badge
                        variant="gradient"
                        :color="SEVERITY_BADGE_CLASSES[alert.severity]"
                        :label="alert.severity"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Date created</strong>
                    </td>
                    <td>
                      {{ stringToPrettyDateTime(alert.created_at) }}
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Date Closed</strong>
                    </td>
                    <td
                      v-if="alert.closed_at"
                    >
                      {{ stringToPrettyDateTime(alert.closed_at) }}
                    </td>
                    <td
                      v-else
                    />
                  </tr>
                  <tr>
                    <td>
                      <strong>Assignee</strong>
                    </td>
                    <td
                      v-if="alert.assignee && !isEditingAssignee"
                      class="text-nowrap overflow-hidden text-truncate"
                      style="max-width: 200px;"
                    >
                      {{ alert.assignee.username }}
                      <i
                        v-if="alert.status !== 'resolved' && alert.status !== 'deleted'"
                        class="fa fa-edit px-3"
                        @click="isEditingAssignee = true;"
                      />
                    </td>
                    <td
                      v-else-if="isEditingAssignee || alert.assignee === null"
                    >
                      <div>
                        <select
                          id="alert-assignee-select"
                          v-model="selectedUserId"
                          class="form-select-sm"
                          @change="_updateAlert(alert.id, { assignee: selectedUserId }); selectedUserId = null"
                        >
                          <option selected disabled>-</option>
                          <option
                            v-for="user in users"
                            :key="user.id"
                            :value="user.id"
                          >
                            {{ user.username }}
                          </option>
                        </select>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="alert"
        class="col-lg-6"
      >
        <timeline-list
          :title="'Timeline of alert: ' + alert.message"
        >
          <timeline-item
            v-for="event in alert.events"
            :key="event.id"
            :icon="{
              component: STATUS_ICONS[event.event_type],
              color: getColor(event),
            }"
            :title="getTitle(event)"
            :date-time="stringToPrettyDateTime(event.timestamp)"
            :badges="[event.event_type]"
          />
        </timeline-list>
      </div>
    </div>
  </div>
  <sh-alert
    v-if="showDialog"
    :title="'Are you sure you want to update this alert?'"
    :text="dialogText"
    icon="warning"
    input="textarea"
    :show-cancel-button="true"
    :confirm-button-text="'Yes'"
    :cancel-button-text="'Cancel'"
    :custom-class="{
      confirmButton: 'btn bg-gradient-success',
      cancelButton: 'btn bg-gradient-warning'
    }"
    @dismissed="showDialog = false"
    @confirmed="_updateAlert(selectedAlertId,
    {
      status: selectedStatus,
      closed_at: selectedStatus === 'deleted' || selectedStatus === 'resolved' ? new Date().toISOString() : null })"
  />
  <sh-alert
    v-if="showConfirm"
    title="Success"
    :text="confirmMessage"
    icon="success"
    confirm-button-text="OK"
    @confirmed="showConfirm = false;"

  />
</template>
