<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

import TopNavbar from './components/navigation/TopNavbar.vue'
import { fetchCurrentUser } from "@/api/users";
import SideNavbar from '@/components/navigation/SideNavbar.vue';

const store = useStore();
const router = useRouter();
const showSidenav = computed(() => store.state.showSidenav);
const showNavbar = computed(() => store.state.showNavbar);

const verifyIfUserIsLoggedIn = async () => {
  const token = localStorage.getItem('token');
  if (token) {
    await _fetchCurrentUser()
  } else {
    await router.push({ name: 'login' });
  }
};

const _fetchCurrentUser = async () => {
  try {
    let response;
    response = await fetchCurrentUser();
     store.commit("user", {
      id: response.data.id,
      username: response.data.username,
      first_name: response.data.first_name,
      last_name: response.data.last_name,
      email: response.data.email,
      phone: response.data.phone,
    });
  } catch (error) {
    console.error('Error fetching current user:', error);
    await router.push({ name: 'login' });
  }
};

onMounted(() => {
  verifyIfUserIsLoggedIn();
});
</script>

<template>
  <side-navbar
    v-if="showSidenav"
  />
  <main class="main-content position-relative max-height-vh-100 h-100">
    <top-navbar v-if="showNavbar"/>
    <div class="container-fluid px-3">
      <router-view/>
    </div>
  </main>
</template>


<style scoped>
@import '../node_modules/bootstrap/dist/css/bootstrap.min.css';
@import '../node_modules/font-awesome/css/font-awesome.min.css';
@import './assets/css/style.css';
</style>
