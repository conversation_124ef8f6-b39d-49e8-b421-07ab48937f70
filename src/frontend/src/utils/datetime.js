import moment from 'moment';
import nl from 'moment/locale/nl';

moment.updateLocale('nl', nl);

export const toPrettyDate = (d) => {
  const month = moment(d).format('MMM').toLowerCase();
  return `${d.getDate()} ${month} ${d.getFullYear()}`;
};

export const toPrettyDateTime = (d) => {
  const month = moment(d).format('MMM').toLowerCase();
  return `${d.getDate()} ${month} ${d.getFullYear()} ${d.getHours()}:${(`0${d.getMinutes()}`).slice(-2)}`;
};

export const stringToPrettyDate = (dString) => {
  if (!dString) {
    return '-';
  }
  const d = new Date(dString);
  return toPrettyDate(d);
};

export const stringToPrettyDateTime = (dString) => {
  if (!dString) {
    return '-';
  }
  const d = new Date(dString);
  return toPrettyDateTime(d);
};
