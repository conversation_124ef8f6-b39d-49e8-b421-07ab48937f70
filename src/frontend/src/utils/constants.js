export const FREQUENCIES = [
  {
    id: 'hourly',
    cron: '0 * * * *',
    name: 'Every hour',
  },
  {
    id: 'once_midnight',
    cron: '0 0 * * *',
    name: 'Once a day at midnight',
  },
  {
    id: 'once_noon',
    cron: '0 12 * * *',
    name: 'Once a day at noon',
  },
  {
    id: 'weekly_monday',
    cron: '0 0 * * 1',
    name: 'Weekly on Monday',
  },
  {
    id: 'monthly_first',
    cron: '0 0 1 * *',
    name: 'Monthly on the first day',
  },
  {
    id: 'yearly',
    cron: '0 0 1 1 *',
    name: 'Yearly',
  }
];

export const RULE_TYPES = [
  {
    id: 'orphan_hosts',
    name: 'Check on orphan hosts',
  },
  {
    id: 'orphan_layers',
    name: 'Check on orphan layers',
  },
  {
    id: 'secret_duplications',
    name: 'Check on same secrets',
  },
  {
    id: 'secret_last_updated',
    name: 'Check how long ago secrets were updated',
  },
  {
    id: 'secret_strength',
    name: 'Check on secret strength',
  },
  {
    id: 'orphan_groups',
    name: 'Check on orphan groups (orphan_groups)',
  },
  {
    id: 'orphan_secrets',
    name: 'Check on orphan secrets',
    disabled: true,
  },
  {
    id: 'unmanaged_users',
    name: 'Check on unmanaged users',
    disabled: true,
  },
  {
    id: 'user_used_secrets',
    name: 'Check on user used secrets',
    disabled: true,
  },
  {
    id: 'suspicious_access_secrets',
    name: 'Check on suspicious access secrets',
    disabled: true,
  },
];

export const ALERT_SEVERITIES = [
  {
    id: 'low',
    name: 'Low',
  },
  {
    id: 'medium',
    name: 'Medium',
  },
  {
    id: 'high',
    name: 'High',
  },
  {
    id: 'critical',
    name: 'Critical',
  },
];

export const SEVERITY_BADGE_CLASSES = {
  low: 'success',
  medium: 'info',
  high: 'warning',
  critical: 'danger',
}

export const STATUS_BADGE_CLASSES = {
  success: 'success',
  resolved: 'success',
  failed: 'danger',
  deleted: 'danger',
  initial: 'warning',
  in_progress: 'info',
}

export const APPLICATIONS = [
  { name: 'Ansible', value: 'ansible' },
  { name: 'Cyberark Conjur', value: 'conjur' },
  { name: 'GitHub', value: 'github' },
  { name: 'GitLab', value: 'gitlab' },
  { name: 'HashiCorp Vault', value: 'vault' },
]