{"name": "vue-argon-dashboard-2-pro", "version": "4.0.0", "private": true, "author": "Creative Tim", "license": "SEE LICENSE IN <https://www.creative-tim.com/license>", "description": "VueJS version of Argon Dashboard 2 PRO by Creative Tim", "homepage": "https://demos.creative-tim.com/vue-argon-dashboard-pro/", "bugs": {"url": "https://github.com/creativetimofficial/ct-vue-argon-dashboard-pro/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/ct-vue-argon-dashboard-pro.git"}, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "deploy": "gh-pages -d dist"}, "dependencies": {"@babel/eslint-parser": "7.27.5", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/vue": "6.1.17", "@fullcalendar/vue3": "6.1.17", "@popperjs/core": "2.11.8", "axios": "^1.10.0", "bootstrap": "5.3.6", "chart.js": "4.5.0", "choices.js": "11.1.0", "core-js": "3", "dragula": "3.7.3", "dropzone": "6.0.0-beta.2", "font-awesome": "^4.7.0", "jkanban": "1.3.1", "leaflet": "1.9.4", "moment": "^2.30.1", "photoswipe": "5.4.4", "quill": "2.0.3", "simple-datatables": "10.0.0", "vue": "3.5.17", "vue-count-to": "1.0.13", "vue-eslint-parser": "9.4.3", "vue-flatpickr-component": "12.0.0", "vue-router": "4.5.1", "vue-star-rating": "2.1.0", "vue-sweetalert2": "5.0.11", "vue-tilt.js": "1.1.1", "vue3-slider": "1.10.1", "vuex": "4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "3.5.17", "@vue/eslint-config-prettier": "9.0.0", "eslint": "8.56.0", "eslint-plugin-prettier": "5.5.0", "eslint-plugin-vue": "9.33.0", "gh-pages": "^6.1.1", "prettier": "3.6.0", "sass": "1.89.2", "sass-loader": "16.0.5"}, "overrides": {"consolidate": "1.0.1"}, "engines": {"node": ">=8 <=21"}}